-- Smile Budget Database Schema - Step by Step
-- Run each section separately in your Supabase SQL Editor

-- ==========================================
-- STEP 1: Create Tables
-- ==========================================

-- 1. Households table
CREATE TABLE IF NOT EXISTS public.households (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    currency_code TEXT DEFAULT 'AUD',
    timezone TEXT DEFAULT 'Australia/Sydney',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. User Profiles table (separate from auth.users)
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    household_id UUID REFERENCES public.households(id) ON DELETE CASCADE,
    full_name TEXT NOT NULL,
    role TEXT DEFAULT 'member',
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Income Sources table
CREATE TABLE IF NOT EXISTS public.income_sources (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    household_id UUID REFERENCES public.households(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    source_type TEXT NOT NULL,
    frequency TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Budget Periods table
CREATE TABLE IF NOT EXISTS public.budget_periods (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    household_id UUID REFERENCES public.households(id) ON DELETE CASCADE,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    status TEXT DEFAULT 'active',
    total_planned_income DECIMAL(10,2) DEFAULT 0,
    total_actual_income DECIMAL(10,2) DEFAULT 0,
    needs_percentage INTEGER DEFAULT 50,
    wants_percentage INTEGER DEFAULT 20,
    goals_percentage INTEGER DEFAULT 25,
    smile_percentage INTEGER DEFAULT 5,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Budget Categories table
CREATE TABLE IF NOT EXISTS public.budget_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    budget_period_id UUID REFERENCES public.budget_periods(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    category_type TEXT NOT NULL,
    icon_name TEXT,
    planned_amount DECIMAL(10,2) DEFAULT 0,
    actual_amount DECIMAL(10,2) DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    template_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Expenses table
CREATE TABLE IF NOT EXISTS public.expenses (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    budget_category_id UUID REFERENCES public.budget_categories(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    description TEXT,
    expense_date DATE DEFAULT CURRENT_DATE,
    payment_method TEXT DEFAULT 'card',
    vendor_name TEXT,
    receipt_url TEXT,
    tags TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);