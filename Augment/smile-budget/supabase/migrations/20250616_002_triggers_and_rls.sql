-- ============================================================================
-- TRIGGER FUNCTIONS FOR REAL-TIME UPDATES
-- ============================================================================

-- Function to update budget category spent amounts
CREATE OR REPLACE FUNCTION update_category_spent_amount()
RETURNS trigger AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Add new expense to category spent amount
        UPDATE budget_categories 
        SET spent_amount = spent_amount + NEW.amount,
            updated_at = NOW()
        WHERE id = NEW.budget_category_id;
        
        -- Log activity
        INSERT INTO activity_log (household_id, user_id, action_type, table_name, record_id, new_values, description)
        SELECT bp.household_id, NEW.user_id, 'INSERT', 'expenses', NEW.id, 
               row_to_json(NEW), 'Added expense: ' || COALESCE(NEW.description, '$' || NEW.amount::text)
        FROM budget_categories bc
        JOIN budget_periods bp ON bc.budget_period_id = bp.id
        WHERE bc.id = NEW.budget_category_id;
        
        RETURN NEW;
        
    ELSIF TG_OP = 'UPDATE' THEN
        -- Adjust category spent amounts for amount changes
        IF OLD.budget_category_id = NEW.budget_category_id THEN
            -- Same category, just update the amount difference
            UPDATE budget_categories 
            SET spent_amount = spent_amount - OLD.amount + NEW.amount,
                updated_at = NOW()
            WHERE id = NEW.budget_category_id;
        ELSE
            -- Different category, remove from old and add to new
            UPDATE budget_categories 
            SET spent_amount = spent_amount - OLD.amount,
                updated_at = NOW()
            WHERE id = OLD.budget_category_id;
            
            UPDATE budget_categories 
            SET spent_amount = spent_amount + NEW.amount,
                updated_at = NOW()
            WHERE id = NEW.budget_category_id;
        END IF;
        
        -- Log activity
        INSERT INTO activity_log (household_id, user_id, action_type, table_name, record_id, old_values, new_values, description)
        SELECT bp.household_id, NEW.user_id, 'UPDATE', 'expenses', NEW.id, 
               row_to_json(OLD), row_to_json(NEW), 'Updated expense: ' || COALESCE(NEW.description, '$' || NEW.amount::text)
        FROM budget_categories bc
        JOIN budget_periods bp ON bc.budget_period_id = bp.id
        WHERE bc.id = NEW.budget_category_id;
        
        RETURN NEW;
        
    ELSIF TG_OP = 'DELETE' THEN
        -- Remove expense from category spent amount
        UPDATE budget_categories 
        SET spent_amount = spent_amount - OLD.amount,
            updated_at = NOW()
        WHERE id = OLD.budget_category_id;
        
        -- Log activity
        INSERT INTO activity_log (household_id, user_id, action_type, table_name, record_id, old_values, description)
        SELECT bp.household_id, OLD.user_id, 'DELETE', 'expenses', OLD.id, 
               row_to_json(OLD), 'Deleted expense: ' || COALESCE(OLD.description, '$' || OLD.amount::text)
        FROM budget_categories bc
        JOIN budget_periods bp ON bc.budget_period_id = bp.id
        WHERE bc.id = OLD.budget_category_id;
        
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to update goal current amounts
CREATE OR REPLACE FUNCTION update_goal_current_amount()
RETURNS trigger AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Add contribution to goal current amount
        UPDATE savings_goals 
        SET current_amount = current_amount + NEW.amount,
            updated_at = NOW()
        WHERE id = NEW.savings_goal_id;
        
        -- Check if goal is achieved
        UPDATE savings_goals 
        SET status = 'achieved',
            updated_at = NOW()
        WHERE id = NEW.savings_goal_id 
        AND current_amount >= target_amount 
        AND status = 'active';
        
        -- Log activity
        INSERT INTO activity_log (household_id, user_id, action_type, table_name, record_id, new_values, description)
        SELECT sg.household_id, NEW.user_id, 'INSERT', 'savings_contributions', NEW.id, 
               row_to_json(NEW), 'Added contribution to ' || sg.name || ': $' || NEW.amount::text
        FROM savings_goals sg
        WHERE sg.id = NEW.savings_goal_id;
        
        RETURN NEW;
        
    ELSIF TG_OP = 'UPDATE' THEN
        -- Adjust goal amount for contribution changes
        UPDATE savings_goals 
        SET current_amount = current_amount - OLD.amount + NEW.amount,
            updated_at = NOW()
        WHERE id = NEW.savings_goal_id;
        
        -- Check achievement status
        UPDATE savings_goals 
        SET status = CASE 
            WHEN current_amount >= target_amount THEN 'achieved'
            WHEN current_amount < target_amount AND status = 'achieved' THEN 'active'
            ELSE status
        END,
        updated_at = NOW()
        WHERE id = NEW.savings_goal_id;
        
        RETURN NEW;
        
    ELSIF TG_OP = 'DELETE' THEN
        -- Remove contribution from goal current amount
        UPDATE savings_goals 
        SET current_amount = current_amount - OLD.amount,
            updated_at = NOW()
        WHERE id = OLD.savings_goal_id;
        
        -- Update achievement status if needed
        UPDATE savings_goals 
        SET status = CASE 
            WHEN current_amount < target_amount AND status = 'achieved' THEN 'active'
            ELSE status
        END,
        updated_at = NOW()
        WHERE id = OLD.savings_goal_id;
        
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS trigger AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to handle user signup
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS trigger AS $$
BEGIN
    -- Create household if user doesn't have one
    IF NEW.raw_user_meta_data->>'household_name' IS NOT NULL THEN
        INSERT INTO households (name, currency_code, timezone)
        VALUES (
            NEW.raw_user_meta_data->>'household_name',
            COALESCE(NEW.raw_user_meta_data->>'currency_code', 'AUD'),
            COALESCE(NEW.raw_user_meta_data->>'timezone', 'Australia/Sydney')
        );
    END IF;
    
    -- Insert user record
    INSERT INTO users (id, household_id, email, full_name, role)
    VALUES (
        NEW.id,
        (SELECT id FROM households ORDER BY created_at DESC LIMIT 1),
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
        'admin' -- First user in household is admin
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- CREATE TRIGGERS
-- ============================================================================

-- Auto-update category spending when expenses change
CREATE TRIGGER trigger_update_category_spent_amount
    AFTER INSERT OR UPDATE OR DELETE ON expenses
    FOR EACH ROW EXECUTE FUNCTION update_category_spent_amount();

-- Auto-update goal progress when contributions change
CREATE TRIGGER trigger_update_goal_current_amount
    AFTER INSERT OR UPDATE OR DELETE ON savings_contributions
    FOR EACH ROW EXECUTE FUNCTION update_goal_current_amount();

-- Auto-update timestamps
CREATE TRIGGER trigger_households_updated_at
    BEFORE UPDATE ON households
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_budget_periods_updated_at
    BEFORE UPDATE ON budget_periods
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_income_sources_updated_at
    BEFORE UPDATE ON income_sources
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_income_entries_updated_at
    BEFORE UPDATE ON income_entries
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_budget_categories_updated_at
    BEFORE UPDATE ON budget_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_expenses_updated_at
    BEFORE UPDATE ON expenses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_savings_goals_updated_at
    BEFORE UPDATE ON savings_goals
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Handle new user signup
CREATE TRIGGER trigger_handle_new_user
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Refresh materialized views when data changes
CREATE TRIGGER trigger_refresh_budget_summary_expenses
    AFTER INSERT OR UPDATE OR DELETE ON expenses
    FOR EACH STATEMENT EXECUTE FUNCTION refresh_budget_summary();

CREATE TRIGGER trigger_refresh_budget_summary_categories
    AFTER UPDATE ON budget_categories
    FOR EACH STATEMENT EXECUTE FUNCTION refresh_budget_summary();

CREATE TRIGGER trigger_refresh_goals_progress_goals
    AFTER UPDATE ON savings_goals
    FOR EACH STATEMENT EXECUTE FUNCTION refresh_goals_progress();

CREATE TRIGGER trigger_refresh_goals_progress_contributions
    AFTER INSERT OR UPDATE OR DELETE ON savings_contributions
    FOR EACH STATEMENT EXECUTE FUNCTION refresh_goals_progress();

-- ============================================================================
-- ROW LEVEL SECURITY POLICIES
-- ============================================================================

-- Households: Users can only access their own household
CREATE POLICY households_access ON households
    FOR ALL USING (
        id IN (
            SELECT household_id FROM users WHERE id = auth.uid()
        )
    );

-- Users: Users can only access members of their household
CREATE POLICY users_access ON users
    FOR ALL USING (
        household_id IN (
            SELECT household_id FROM users WHERE id = auth.uid()
        )
    );

-- Budget Periods: Users can only access their household's budget periods
CREATE POLICY budget_periods_access ON budget_periods
    FOR ALL USING (
        household_id IN (
            SELECT household_id FROM users WHERE id = auth.uid()
        )
    );

-- Income Sources: Users can only access their household's income sources
CREATE POLICY income_sources_access ON income_sources
    FOR ALL USING (
        household_id IN (
            SELECT household_id FROM users WHERE id = auth.uid()
        )
    );

-- Income Entries: Users can only access their household's income entries
CREATE POLICY income_entries_access ON income_entries
    FOR ALL USING (
        budget_period_id IN (
            SELECT bp.id FROM budget_periods bp
            JOIN users u ON bp.household_id = u.household_id
            WHERE u.id = auth.uid()
        )
    );

-- Budget Categories: Users can only access their household's budget categories
CREATE POLICY budget_categories_access ON budget_categories
    FOR ALL USING (
        budget_period_id IN (
            SELECT bp.id FROM budget_periods bp
            JOIN users u ON bp.household_id = u.household_id
            WHERE u.id = auth.uid()
        )
    );

-- Expenses: Users can only access their household's expenses
CREATE POLICY expenses_access ON expenses
    FOR ALL USING (
        budget_category_id IN (
            SELECT bc.id FROM budget_categories bc
            JOIN budget_periods bp ON bc.budget_period_id = bp.id
            JOIN users u ON bp.household_id = u.household_id
            WHERE u.id = auth.uid()
        )
    );

-- Savings Goals: Users can only access their household's savings goals
CREATE POLICY savings_goals_access ON savings_goals
    FOR ALL USING (
        household_id IN (
            SELECT household_id FROM users WHERE id = auth.uid()
        )
    );

-- Savings Contributions: Users can only access their household's contributions
CREATE POLICY savings_contributions_access ON savings_contributions
    FOR ALL USING (
        savings_goal_id IN (
            SELECT sg.id FROM savings_goals sg
            JOIN users u ON sg.household_id = u.household_id
            WHERE u.id = auth.uid()
        )
    );

-- Activity Log: Users can only access their household's activity log
CREATE POLICY activity_log_access ON activity_log
    FOR ALL USING (
        household_id IN (
            SELECT household_id FROM users WHERE id = auth.uid()
        )
    );

-- User Invitations: Users can only access invitations for their household
CREATE POLICY user_invitations_access ON user_invitations
    FOR ALL USING (
        household_id IN (
            SELECT household_id FROM users WHERE id = auth.uid()
        )
    );

-- ============================================================================
-- HELPER FUNCTIONS FOR APPLICATION
-- ============================================================================

-- Get complete dashboard data for a household
CREATE OR REPLACE FUNCTION get_household_dashboard(household_uuid UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    -- Check user has access to this household
    IF NOT EXISTS (
        SELECT 1 FROM users 
        WHERE id = auth.uid() AND household_id = household_uuid
    ) THEN
        RAISE EXCEPTION 'Access denied to household %', household_uuid;
    END IF;
    
    -- Build complete dashboard data
    SELECT json_build_object(
        'household', (
            SELECT row_to_json(h) FROM households h WHERE h.id = household_uuid
        ),
        'current_budget', (
            SELECT row_to_json(bs) FROM budget_summary bs 
            WHERE bs.household_id = household_uuid 
            AND bs.period_start <= CURRENT_DATE 
            AND bs.period_end >= CURRENT_DATE
            LIMIT 1
        ),
        'income_summary', (
            SELECT json_agg(json_build_object(
                'source_name', isc.name,
                'expected_amount', isc.expected_amount,
                'actual_amount', COALESCE(ie_sum.total_received, 0),
                'frequency', isc.frequency,
                'variance', COALESCE(ie_sum.total_received, 0) - isc.expected_amount
            ))
            FROM income_sources isc
            LEFT JOIN (
                SELECT 
                    ie.income_source_id,
                    SUM(ie.amount) as total_received
                FROM income_entries ie
                JOIN budget_periods bp ON ie.budget_period_id = bp.id
                WHERE bp.household_id = household_uuid
                AND bp.status = 'active'
                GROUP BY ie.income_source_id
            ) ie_sum ON isc.id = ie_sum.income_source_id
            WHERE isc.household_id = household_uuid
            AND isc.is_active = TRUE
        ),
        'category_breakdown', (
            SELECT json_agg(json_build_object(
                'id', bc.id,
                'name', bc.name,
                'type', bc.category_type,
                'allocated', bc.allocated_amount,
                'spent', bc.spent_amount,
                'remaining', bc.allocated_amount - bc.spent_amount,
                'icon', bc.icon_name,
                'color', bc.color_theme,
                'progress_percentage', 
                    CASE WHEN bc.allocated_amount > 0 
                    THEN ROUND((bc.spent_amount / bc.allocated_amount) * 100, 1)
                    ELSE 0 END
            ) ORDER BY bc.sort_order)
            FROM budget_categories bc
            JOIN budget_periods bp ON bc.budget_period_id = bp.id
            WHERE bp.household_id = household_uuid
            AND bp.status = 'active'
            AND bc.is_active = TRUE
        ),
        'active_goals', (
            SELECT json_agg(json_build_object(
                'id', gp.goal_id,
                'name', gp.name,
                'target_amount', gp.target_amount,
                'current_amount', gp.current_amount,
                'progress_percentage', gp.progress_percentage,
                'months_to_completion', gp.months_to_completion,
                'is_achieved', gp.is_achieved,
                'priority_level', gp.priority_level
            ) ORDER BY gp.priority_level)
            FROM goals_progress gp
            WHERE gp.household_id = household_uuid
            AND gp.progress_percentage > 0
            LIMIT 5
        ),
        'recent_expenses', (
            SELECT json_agg(json_build_object(
                'id', e.id,
                'amount', e.amount,
                'description', e.description,
                'vendor_name', e.vendor_name,
                'expense_date', e.expense_date,
                'category_name', bc.name,
                'category_type', bc.category_type,
                'user_name', u.full_name
            ) ORDER BY e.created_at DESC)
            FROM expenses e
            JOIN budget_categories bc ON e.budget_category_id = bc.id
            JOIN budget_periods bp ON bc.budget_period_id = bp.id
            JOIN users u ON e.user_id = u.id
            WHERE bp.household_id = household_uuid
            AND e.expense_date >= CURRENT_DATE - INTERVAL '7 days'
            ORDER BY e.created_at DESC
            LIMIT 10
        )
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create budget for new period
CREATE OR REPLACE FUNCTION create_new_budget_period(
    household_uuid UUID,
    start_date DATE,
    end_date DATE,
    planned_income DECIMAL DEFAULT 0,
    allocations JSONB DEFAULT '{"needs": 50, "wants": 20, "goals": 25, "smile": 5}'::jsonb
) RETURNS UUID AS $$
DECLARE
    new_budget_id UUID;
    template_rec RECORD;
    allocation_amount DECIMAL;
BEGIN
    -- Check user has admin access
    IF NOT EXISTS (
        SELECT 1 FROM users 
        WHERE id = auth.uid() 
        AND household_id = household_uuid 
        AND role = 'admin'
    ) THEN
        RAISE EXCEPTION 'Admin access required to create budget periods';
    END IF;
    
    -- Create budget period
    INSERT INTO budget_periods (
        household_id, period_start, period_end, total_planned_income,
        needs_percentage, wants_percentage, goals_percentage, smile_percentage
    ) VALUES (
        household_uuid, start_date, end_date, planned_income,
        (allocations->>'needs')::decimal,
        (allocations->>'wants')::decimal,
        (allocations->>'goals')::decimal,
        (allocations->>'smile')::decimal
    ) RETURNING id INTO new_budget_id;
    
    -- Create categories from templates
    FOR template_rec IN 
        SELECT * FROM category_templates 
        WHERE is_system_template = TRUE 
        ORDER BY category_type, name
    LOOP
        -- Calculate allocation amount based on percentage and income
        allocation_amount := planned_income * 
            (allocations->>template_rec.category_type)::decimal / 100 *
            COALESCE(template_rec.suggested_percentage, 0) / 100;
            
        INSERT INTO budget_categories (
            budget_period_id, template_id, name, category_type,
            allocated_amount, icon_name, color_theme
        ) VALUES (
            new_budget_id, template_rec.id, template_rec.name, template_rec.category_type,
            allocation_amount, template_rec.icon_name, template_rec.color_theme
        );
    END LOOP;
    
    RETURN new_budget_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;