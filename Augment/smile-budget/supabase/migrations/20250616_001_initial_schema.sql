-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enable RLS globally
ALTER DEFAULT PRIVILEGES IN SCHEMA public REVOKE EXECUTE ON FUNCTIONS FROM public;

-- ============================================================================
-- HOUSEHOLDS TABLE - Family units with multi-currency support
-- ============================================================================
CREATE TABLE households (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    currency_code CHAR(3) DEFAULT 'AUD' NOT NULL,
    timezone VARCHAR(50) DEFAULT 'Australia/Sydney',
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    CONSTRAINT valid_currency CHECK (currency_code IN ('AUD', 'USD', 'EUR', 'GBP', 'CAD', 'NZD'))
);

-- Enable RLS
ALTER TABLE households ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- USERS TABLE - Family members with role-based access
-- ============================================================================
CREATE TABLE users (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    household_id UUID REFERENCES households(id) ON DELETE CASCADE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role VARCHAR(20) DEFAULT 'member' NOT NULL,
    avatar_url TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    CONSTRAINT valid_role CHECK (role IN ('admin', 'member', 'viewer'))
);

-- Enable RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- BUDGET PERIODS TABLE - Monthly budgets with flexible allocations
-- ============================================================================
CREATE TABLE budget_periods (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    household_id UUID REFERENCES households(id) ON DELETE CASCADE NOT NULL,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    status VARCHAR(20) DEFAULT 'active' NOT NULL,
    total_planned_income DECIMAL(12,2) DEFAULT 0.00 NOT NULL,
    total_actual_income DECIMAL(12,2) DEFAULT 0.00 NOT NULL,
    needs_percentage DECIMAL(5,2) DEFAULT 50.00 NOT NULL,
    wants_percentage DECIMAL(5,2) DEFAULT 20.00 NOT NULL,
    goals_percentage DECIMAL(5,2) DEFAULT 25.00 NOT NULL,
    smile_percentage DECIMAL(5,2) DEFAULT 5.00 NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    CONSTRAINT valid_status CHECK (status IN ('draft', 'active', 'completed', 'archived')),
    CONSTRAINT valid_percentages CHECK (
        needs_percentage + wants_percentage + goals_percentage + smile_percentage = 100.00
    ),
    CONSTRAINT valid_percentage_ranges CHECK (
        needs_percentage >= 0 AND needs_percentage <= 100 AND
        wants_percentage >= 0 AND wants_percentage <= 100 AND
        goals_percentage >= 0 AND goals_percentage <= 100 AND
        smile_percentage >= 0 AND smile_percentage <= 100
    ),
    CONSTRAINT valid_income CHECK (total_planned_income >= 0 AND total_actual_income >= 0),
    CONSTRAINT valid_period CHECK (period_start <= period_end)
);

-- Enable RLS
ALTER TABLE budget_periods ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- INCOME SOURCES TABLE - Recurring and one-time income streams
-- ============================================================================
CREATE TABLE income_sources (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    household_id UUID REFERENCES households(id) ON DELETE CASCADE NOT NULL,
    name VARCHAR(100) NOT NULL,
    source_type VARCHAR(20) DEFAULT 'salary' NOT NULL,
    frequency VARCHAR(20) DEFAULT 'monthly' NOT NULL,
    expected_amount DECIMAL(10,2) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    CONSTRAINT valid_source_type CHECK (source_type IN ('salary', 'freelance', 'investment', 'rental', 'business', 'pension', 'other')),
    CONSTRAINT valid_frequency CHECK (frequency IN ('weekly', 'fortnightly', 'monthly', 'quarterly', 'annually', 'irregular')),
    CONSTRAINT valid_amount CHECK (expected_amount > 0)
);

-- Enable RLS
ALTER TABLE income_sources ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- INCOME ENTRIES TABLE - Actual income transactions
-- ============================================================================
CREATE TABLE income_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    income_source_id UUID REFERENCES income_sources(id) ON DELETE CASCADE NOT NULL,
    budget_period_id UUID REFERENCES budget_periods(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE RESTRICT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    received_date DATE DEFAULT CURRENT_DATE NOT NULL,
    description VARCHAR(500),
    reference_number VARCHAR(100),
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    CONSTRAINT valid_amount CHECK (amount > 0)
);

-- Enable RLS
ALTER TABLE income_entries ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- CATEGORY TEMPLATES TABLE - Reusable category definitions
-- ============================================================================
CREATE TABLE category_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    category_type VARCHAR(10) NOT NULL,
    icon_name VARCHAR(50),
    color_theme VARCHAR(20),
    description TEXT,
    suggested_percentage DECIMAL(5,2),
    is_system_template BOOLEAN DEFAULT FALSE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    CONSTRAINT valid_category_type CHECK (category_type IN ('needs', 'wants', 'goals', 'smile')),
    CONSTRAINT valid_percentage CHECK (suggested_percentage IS NULL OR (suggested_percentage >= 0 AND suggested_percentage <= 100))
);

-- Insert system templates
INSERT INTO category_templates (name, category_type, icon_name, color_theme, description, suggested_percentage, is_system_template) VALUES
-- NEEDS Categories
('Housing & Utilities', 'needs', 'House', 'needs', 'Rent, mortgage, electricity, gas, water, rates', 25.00, TRUE),
('Transport', 'needs', 'Car', 'needs', 'Car payments, fuel, public transport, maintenance', 10.00, TRUE),
('Groceries & Food', 'needs', 'ShoppingCart', 'needs', 'Supermarket shopping, essential food items', 8.00, TRUE),
('Healthcare', 'needs', 'Heart', 'needs', 'Medical expenses, insurance, prescriptions', 3.00, TRUE),
('Insurance', 'needs', 'Umbrella', 'needs', 'Health, car, home, life insurance premiums', 2.00, TRUE),
('Education', 'needs', 'GraduationCap', 'needs', 'School fees, courses, educational materials', 2.00, TRUE),

-- WANTS Categories
('Entertainment', 'wants', 'GameController', 'wants', 'Movies, games, streaming services, hobbies', 5.00, TRUE),
('Dining Out', 'wants', 'ForkKnife', 'wants', 'Restaurants, cafes, takeaway food', 3.00, TRUE),
('Personal Care', 'wants', 'Scissors', 'wants', 'Haircuts, beauty, clothing, accessories', 4.00, TRUE),
('Subscriptions', 'wants', 'Television', 'wants', 'Streaming, magazines, gym memberships', 2.00, TRUE),
('Shopping', 'wants', 'ShoppingBag', 'wants', 'Non-essential purchases, gadgets, home decor', 3.00, TRUE),
('Social Activities', 'wants', 'Users', 'wants', 'Socializing, events, recreational activities', 3.00, TRUE),

-- GOALS Categories
('Emergency Fund', 'goals', 'Umbrella', 'goals', '6-month expense safety net', 10.00, TRUE),
('House Deposit', 'goals', 'Buildings', 'goals', 'Saving for home purchase deposit', 5.00, TRUE),
('Investments', 'goals', 'TrendingUp', 'goals', 'Shares, ETFs, investment accounts', 5.00, TRUE),
('Retirement', 'goals', 'Armchair', 'goals', 'Superannuation, retirement savings', 3.00, TRUE),
('Car Replacement', 'goals', 'Car', 'goals', 'Saving for next vehicle purchase', 2.00, TRUE),

-- SMILE Categories
('Travel & Holidays', 'smile', 'Airplane', 'smile', 'Vacations, weekend trips, travel experiences', 3.00, TRUE),
('Family Experiences', 'smile', 'Balloon', 'smile', 'Activities, outings, special family moments', 1.00, TRUE),
('Treats & Surprises', 'smile', 'Gift', 'smile', 'Spontaneous purchases, gifts, indulgences', 1.00, TRUE);

-- ============================================================================
-- BUDGET CATEGORIES TABLE - Active categories for budget periods
-- ============================================================================
CREATE TABLE budget_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    budget_period_id UUID REFERENCES budget_periods(id) ON DELETE CASCADE NOT NULL,
    template_id UUID REFERENCES category_templates(id) ON DELETE RESTRICT,
    name VARCHAR(100) NOT NULL,
    category_type VARCHAR(10) NOT NULL,
    allocated_amount DECIMAL(10,2) NOT NULL,
    spent_amount DECIMAL(10,2) DEFAULT 0.00 NOT NULL,
    target_amount DECIMAL(10,2), -- For goals/smile accumulating categories
    current_balance DECIMAL(10,2) DEFAULT 0.00 NOT NULL, -- For accumulating categories
    icon_name VARCHAR(50),
    color_theme VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    sort_order INTEGER DEFAULT 0 NOT NULL,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    CONSTRAINT valid_category_type CHECK (category_type IN ('needs', 'wants', 'goals', 'smile')),
    CONSTRAINT valid_amounts CHECK (
        allocated_amount >= 0 AND 
        spent_amount >= 0 AND 
        current_balance >= 0 AND
        (target_amount IS NULL OR target_amount > 0)
    ),
    CONSTRAINT valid_spent_vs_allocated CHECK (spent_amount <= allocated_amount OR category_type IN ('goals', 'smile'))
);

-- Enable RLS
ALTER TABLE budget_categories ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- EXPENSES TABLE - All spending transactions (hot table)
-- ============================================================================
CREATE TABLE expenses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    budget_category_id UUID REFERENCES budget_categories(id) ON DELETE RESTRICT NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE RESTRICT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    description VARCHAR(500),
    expense_date DATE DEFAULT CURRENT_DATE NOT NULL,
    payment_method VARCHAR(30) DEFAULT 'unknown',
    vendor_name VARCHAR(200),
    receipt_url TEXT,
    tags TEXT[], -- Flexible categorization
    location_data JSONB, -- Geolocation for location-based features
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    CONSTRAINT valid_amount CHECK (amount > 0),
    CONSTRAINT valid_payment_method CHECK (payment_method IN ('cash', 'card', 'transfer', 'paypal', 'afterpay', 'unknown'))
);

-- Enable RLS
ALTER TABLE expenses ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- SAVINGS GOALS TABLE - Long-term financial targets
-- ============================================================================
CREATE TABLE savings_goals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    household_id UUID REFERENCES households(id) ON DELETE CASCADE NOT NULL,
    budget_category_id UUID REFERENCES budget_categories(id) ON DELETE SET NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    target_amount DECIMAL(12,2) NOT NULL,
    current_amount DECIMAL(12,2) DEFAULT 0.00 NOT NULL,
    monthly_contribution DECIMAL(10,2) DEFAULT 0.00 NOT NULL,
    target_date DATE,
    priority_level INTEGER DEFAULT 3 NOT NULL,
    status VARCHAR(20) DEFAULT 'active' NOT NULL,
    celebration_message TEXT,
    image_url TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    CONSTRAINT valid_amounts CHECK (
        target_amount > 0 AND 
        current_amount >= 0 AND 
        monthly_contribution >= 0 AND
        current_amount <= target_amount
    ),
    CONSTRAINT valid_priority CHECK (priority_level >= 1 AND priority_level <= 5),
    CONSTRAINT valid_status CHECK (status IN ('draft', 'active', 'achieved', 'paused', 'cancelled'))
);

-- Enable RLS
ALTER TABLE savings_goals ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- SAVINGS CONTRIBUTIONS TABLE - Goal progress tracking
-- ============================================================================
CREATE TABLE savings_contributions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    savings_goal_id UUID REFERENCES savings_goals(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE RESTRICT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    contribution_date DATE DEFAULT CURRENT_DATE NOT NULL,
    contribution_type VARCHAR(20) DEFAULT 'manual' NOT NULL,
    description VARCHAR(500),
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    CONSTRAINT valid_amount CHECK (amount > 0),
    CONSTRAINT valid_contribution_type CHECK (contribution_type IN ('manual', 'automatic', 'bonus', 'interest'))
);

-- Enable RLS
ALTER TABLE savings_contributions ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- ACTIVITY LOG TABLE - Complete audit trail
-- ============================================================================
CREATE TABLE activity_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    household_id UUID REFERENCES households(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    action_type VARCHAR(50) NOT NULL,
    table_name VARCHAR(50) NOT NULL,
    record_id UUID NOT NULL,
    old_values JSONB,
    new_values JSONB,
    description TEXT,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    CONSTRAINT valid_action_type CHECK (action_type IN ('INSERT', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT', 'INVITE', 'JOIN'))
);

-- Enable RLS
ALTER TABLE activity_log ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- USER INVITATIONS TABLE - Family collaboration management
-- ============================================================================
CREATE TABLE user_invitations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    household_id UUID REFERENCES households(id) ON DELETE CASCADE NOT NULL,
    invited_by UUID REFERENCES users(id) ON DELETE SET NULL NOT NULL,
    email VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'member' NOT NULL,
    invitation_token UUID DEFAULT uuid_generate_v4() NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' NOT NULL,
    expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '7 days') NOT NULL,
    accepted_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    CONSTRAINT valid_role CHECK (role IN ('admin', 'member', 'viewer')),
    CONSTRAINT valid_status CHECK (status IN ('pending', 'accepted', 'expired', 'cancelled')),
    CONSTRAINT unique_pending_invitation UNIQUE (household_id, email, status) DEFERRABLE INITIALLY DEFERRED
);

-- Enable RLS
ALTER TABLE user_invitations ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- PERFORMANCE INDEXES
-- ============================================================================

-- Households
CREATE INDEX idx_households_created_at ON households(created_at DESC);

-- Users
CREATE INDEX idx_users_household_id ON users(household_id);
CREATE INDEX idx_users_email ON users(email);

-- Budget Periods
CREATE INDEX idx_budget_periods_household_id ON budget_periods(household_id);
CREATE INDEX idx_budget_periods_status ON budget_periods(status);
CREATE INDEX idx_budget_periods_dates ON budget_periods(period_start, period_end);
CREATE INDEX idx_budget_periods_active ON budget_periods(household_id, status) WHERE status = 'active';

-- Income Sources
CREATE INDEX idx_income_sources_household_id ON income_sources(household_id);
CREATE INDEX idx_income_sources_active ON income_sources(household_id, is_active) WHERE is_active = TRUE;

-- Income Entries
CREATE INDEX idx_income_entries_budget_period ON income_entries(budget_period_id);
CREATE INDEX idx_income_entries_source ON income_entries(income_source_id);
CREATE INDEX idx_income_entries_date ON income_entries(received_date DESC);

-- Category Templates
CREATE INDEX idx_category_templates_type ON category_templates(category_type);
CREATE INDEX idx_category_templates_system ON category_templates(is_system_template);

-- Budget Categories
CREATE INDEX idx_budget_categories_period ON budget_categories(budget_period_id);
CREATE INDEX idx_budget_categories_type ON budget_categories(category_type);
CREATE INDEX idx_budget_categories_active ON budget_categories(budget_period_id, is_active) WHERE is_active = TRUE;
CREATE INDEX idx_budget_categories_sort ON budget_categories(budget_period_id, sort_order);

-- Expenses (Hot table - optimized for frequent queries)
CREATE INDEX idx_expenses_category_date ON expenses(budget_category_id, expense_date DESC);
CREATE INDEX idx_expenses_user_date ON expenses(user_id, expense_date DESC);
CREATE INDEX idx_expenses_date_amount ON expenses(expense_date DESC, amount DESC);
CREATE INDEX idx_expenses_vendor ON expenses USING gin(vendor_name gin_trgm_ops) WHERE vendor_name IS NOT NULL;
CREATE INDEX idx_expenses_tags ON expenses USING gin(tags) WHERE tags IS NOT NULL;
CREATE INDEX idx_expenses_recent ON expenses(created_at DESC) WHERE created_at > NOW() - INTERVAL '30 days';

-- Savings Goals
CREATE INDEX idx_savings_goals_household_id ON savings_goals(household_id);
CREATE INDEX idx_savings_goals_status ON savings_goals(status);
CREATE INDEX idx_savings_goals_priority ON savings_goals(household_id, priority_level, status);
CREATE INDEX idx_savings_goals_target_date ON savings_goals(target_date) WHERE target_date IS NOT NULL;

-- Savings Contributions
CREATE INDEX idx_savings_contributions_goal ON savings_contributions(savings_goal_id);
CREATE INDEX idx_savings_contributions_date ON savings_contributions(contribution_date DESC);

-- Activity Log
CREATE INDEX idx_activity_log_household ON activity_log(household_id, created_at DESC);
CREATE INDEX idx_activity_log_user ON activity_log(user_id, created_at DESC);
CREATE INDEX idx_activity_log_table ON activity_log(table_name, record_id);

-- User Invitations
CREATE INDEX idx_user_invitations_household ON user_invitations(household_id);
CREATE INDEX idx_user_invitations_token ON user_invitations(invitation_token);
CREATE INDEX idx_user_invitations_email ON user_invitations(email);
CREATE INDEX idx_user_invitations_pending ON user_invitations(status, expires_at) WHERE status = 'pending';

-- ============================================================================
-- MATERIALIZED VIEWS FOR PERFORMANCE
-- ============================================================================

-- Budget summary for lightning-fast dashboard
CREATE MATERIALIZED VIEW budget_summary AS
SELECT 
    bp.id as budget_period_id,
    bp.household_id,
    bp.period_start,
    bp.period_end,
    bp.total_actual_income,
    bp.needs_percentage,
    bp.wants_percentage,
    bp.goals_percentage,
    bp.smile_percentage,
    
    -- Category spending summaries
    SUM(CASE WHEN bc.category_type = 'needs' THEN bc.spent_amount ELSE 0 END) as needs_spent,
    SUM(CASE WHEN bc.category_type = 'wants' THEN bc.spent_amount ELSE 0 END) as wants_spent,
    SUM(CASE WHEN bc.category_type = 'goals' THEN bc.spent_amount ELSE 0 END) as goals_spent,
    SUM(CASE WHEN bc.category_type = 'smile' THEN bc.spent_amount ELSE 0 END) as smile_spent,
    
    -- Allocated amounts
    SUM(CASE WHEN bc.category_type = 'needs' THEN bc.allocated_amount ELSE 0 END) as needs_allocated,
    SUM(CASE WHEN bc.category_type = 'wants' THEN bc.allocated_amount ELSE 0 END) as wants_allocated,
    SUM(CASE WHEN bc.category_type = 'goals' THEN bc.allocated_amount ELSE 0 END) as goals_allocated,
    SUM(CASE WHEN bc.category_type = 'smile' THEN bc.allocated_amount ELSE 0 END) as smile_allocated,
    
    -- Remaining income
    bp.total_actual_income - SUM(bc.spent_amount) as remaining_income,
    
    -- Updated timestamp
    bp.updated_at
FROM budget_periods bp
LEFT JOIN budget_categories bc ON bp.id = bc.budget_period_id AND bc.is_active = TRUE
WHERE bp.status IN ('active', 'completed')
GROUP BY bp.id, bp.household_id, bp.period_start, bp.period_end, bp.total_actual_income, 
         bp.needs_percentage, bp.wants_percentage, bp.goals_percentage, bp.smile_percentage, bp.updated_at;

-- Create index on materialized view
CREATE UNIQUE INDEX idx_budget_summary_period ON budget_summary(budget_period_id);
CREATE INDEX idx_budget_summary_household ON budget_summary(household_id);

-- Goals progress for celebration features
CREATE MATERIALIZED VIEW goals_progress AS
SELECT 
    sg.id as goal_id,
    sg.household_id,
    sg.name,
    sg.description,
    sg.target_amount,
    sg.current_amount,
    sg.monthly_contribution,
    sg.target_date,
    sg.priority_level,
    sg.status,
    
    -- Progress calculations
    ROUND((sg.current_amount / sg.target_amount) * 100, 2) as progress_percentage,
    sg.target_amount - sg.current_amount as remaining_amount,
    
    -- Time calculations
    CASE 
        WHEN sg.monthly_contribution > 0 AND sg.current_amount < sg.target_amount
        THEN CEIL((sg.target_amount - sg.current_amount) / sg.monthly_contribution)
        ELSE NULL 
    END as months_to_completion,
    
    -- Achievement status
    CASE 
        WHEN sg.current_amount >= sg.target_amount THEN TRUE
        ELSE FALSE
    END as is_achieved,
    
    -- Last contribution info
    (SELECT MAX(contribution_date) FROM savings_contributions sc WHERE sc.savings_goal_id = sg.id) as last_contribution_date,
    (SELECT SUM(amount) FROM savings_contributions sc WHERE sc.savings_goal_id = sg.id) as total_contributions,
    
    sg.updated_at
FROM savings_goals sg
WHERE sg.status IN ('active', 'achieved')
ORDER BY sg.priority_level ASC, sg.target_date ASC NULLS LAST;

-- Create index on materialized view
CREATE UNIQUE INDEX idx_goals_progress_goal ON goals_progress(goal_id);
CREATE INDEX idx_goals_progress_household ON goals_progress(household_id);
CREATE INDEX idx_goals_progress_status ON goals_progress(household_id, is_achieved, priority_level);

-- ============================================================================
-- REFRESH POLICIES FOR MATERIALIZED VIEWS
-- ============================================================================

-- Refresh budget_summary when budget data changes
CREATE OR REPLACE FUNCTION refresh_budget_summary()
RETURNS trigger AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY budget_summary;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Refresh goals_progress when goal data changes
CREATE OR REPLACE FUNCTION refresh_goals_progress()
RETURNS trigger AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY goals_progress;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;