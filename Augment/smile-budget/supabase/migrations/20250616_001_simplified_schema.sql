-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- ============================================================================
-- HOUSEHOLDS TABLE - Family units with multi-currency support
-- ============================================================================
CREATE TABLE households (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    currency_code CHAR(3) DEFAULT 'AUD' NOT NULL,
    timezone VARCHAR(50) DEFAULT 'Australia/Sydney',
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    CONSTRAINT valid_currency CHECK (currency_code IN ('AUD', 'USD', 'EUR', 'GBP', 'CAD', 'NZD'))
);

-- Enable RLS
ALTER TABLE households ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- USERS TABLE - Family members with role-based access
-- ============================================================================
CREATE TABLE users (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    household_id UUID REFERENCES households(id) ON DELETE CASCADE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role VARCHAR(20) DEFAULT 'member' NOT NULL,
    avatar_url TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    CONSTRAINT valid_role CHECK (role IN ('admin', 'member', 'viewer'))
);

-- Enable RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- BUDGET PERIODS TABLE - Monthly budgets with flexible allocations
-- ============================================================================
CREATE TABLE budget_periods (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    household_id UUID REFERENCES households(id) ON DELETE CASCADE NOT NULL,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    status VARCHAR(20) DEFAULT 'active' NOT NULL,
    total_planned_income DECIMAL(12,2) DEFAULT 0.00 NOT NULL,
    total_actual_income DECIMAL(12,2) DEFAULT 0.00 NOT NULL,
    needs_percentage DECIMAL(5,2) DEFAULT 50.00 NOT NULL,
    wants_percentage DECIMAL(5,2) DEFAULT 20.00 NOT NULL,
    goals_percentage DECIMAL(5,2) DEFAULT 25.00 NOT NULL,
    smile_percentage DECIMAL(5,2) DEFAULT 5.00 NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    CONSTRAINT valid_status CHECK (status IN ('draft', 'active', 'completed', 'archived')),
    CONSTRAINT valid_percentages CHECK (
        needs_percentage + wants_percentage + goals_percentage + smile_percentage = 100.00
    ),
    CONSTRAINT valid_income CHECK (total_planned_income >= 0 AND total_actual_income >= 0),
    CONSTRAINT valid_period CHECK (period_start <= period_end)
);

-- Enable RLS
ALTER TABLE budget_periods ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- INCOME SOURCES TABLE - Recurring and one-time income streams
-- ============================================================================
CREATE TABLE income_sources (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    household_id UUID REFERENCES households(id) ON DELETE CASCADE NOT NULL,
    name VARCHAR(100) NOT NULL,
    source_type VARCHAR(20) DEFAULT 'salary' NOT NULL,
    frequency VARCHAR(20) DEFAULT 'monthly' NOT NULL,
    expected_amount DECIMAL(10,2) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    CONSTRAINT valid_source_type CHECK (source_type IN ('salary', 'freelance', 'investment', 'rental', 'business', 'pension', 'other')),
    CONSTRAINT valid_frequency CHECK (frequency IN ('weekly', 'fortnightly', 'monthly', 'quarterly', 'annually', 'irregular')),
    CONSTRAINT valid_amount CHECK (expected_amount > 0)
);

-- Enable RLS
ALTER TABLE income_sources ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- CATEGORY TEMPLATES TABLE - Reusable category definitions
-- ============================================================================
CREATE TABLE category_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    category_type VARCHAR(10) NOT NULL,
    icon_name VARCHAR(50),
    color_theme VARCHAR(20),
    description TEXT,
    suggested_percentage DECIMAL(5,2),
    is_system_template BOOLEAN DEFAULT FALSE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    CONSTRAINT valid_category_type CHECK (category_type IN ('needs', 'wants', 'goals', 'smile')),
    CONSTRAINT valid_percentage CHECK (suggested_percentage IS NULL OR (suggested_percentage >= 0 AND suggested_percentage <= 100))
);

-- ============================================================================
-- BUDGET CATEGORIES TABLE - Active categories for budget periods
-- ============================================================================
CREATE TABLE budget_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    budget_period_id UUID REFERENCES budget_periods(id) ON DELETE CASCADE NOT NULL,
    template_id UUID REFERENCES category_templates(id) ON DELETE RESTRICT,
    name VARCHAR(100) NOT NULL,
    category_type VARCHAR(10) NOT NULL,
    allocated_amount DECIMAL(10,2) NOT NULL,
    spent_amount DECIMAL(10,2) DEFAULT 0.00 NOT NULL,
    target_amount DECIMAL(10,2), -- For goals/smile accumulating categories
    current_balance DECIMAL(10,2) DEFAULT 0.00 NOT NULL, -- For accumulating categories
    icon_name VARCHAR(50),
    color_theme VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    sort_order INTEGER DEFAULT 0 NOT NULL,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    CONSTRAINT valid_category_type CHECK (category_type IN ('needs', 'wants', 'goals', 'smile')),
    CONSTRAINT valid_amounts CHECK (
        allocated_amount >= 0 AND 
        spent_amount >= 0 AND 
        current_balance >= 0 AND
        (target_amount IS NULL OR target_amount > 0)
    )
);

-- Enable RLS
ALTER TABLE budget_categories ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- EXPENSES TABLE - All spending transactions
-- ============================================================================
CREATE TABLE expenses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    budget_category_id UUID REFERENCES budget_categories(id) ON DELETE RESTRICT NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE RESTRICT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    description VARCHAR(500),
    expense_date DATE DEFAULT CURRENT_DATE NOT NULL,
    payment_method VARCHAR(30) DEFAULT 'unknown',
    vendor_name VARCHAR(200),
    receipt_url TEXT,
    tags TEXT[],
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    CONSTRAINT valid_amount CHECK (amount > 0),
    CONSTRAINT valid_payment_method CHECK (payment_method IN ('cash', 'card', 'transfer', 'paypal', 'afterpay', 'unknown'))
);

-- Enable RLS
ALTER TABLE expenses ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- SAVINGS GOALS TABLE - Long-term financial targets
-- ============================================================================
CREATE TABLE savings_goals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    household_id UUID REFERENCES households(id) ON DELETE CASCADE NOT NULL,
    budget_category_id UUID REFERENCES budget_categories(id) ON DELETE SET NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    target_amount DECIMAL(12,2) NOT NULL,
    current_amount DECIMAL(12,2) DEFAULT 0.00 NOT NULL,
    monthly_contribution DECIMAL(10,2) DEFAULT 0.00 NOT NULL,
    target_date DATE,
    priority_level INTEGER DEFAULT 3 NOT NULL,
    status VARCHAR(20) DEFAULT 'active' NOT NULL,
    celebration_message TEXT,
    image_url TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    CONSTRAINT valid_amounts CHECK (
        target_amount > 0 AND 
        current_amount >= 0 AND 
        monthly_contribution >= 0 AND
        current_amount <= target_amount
    ),
    CONSTRAINT valid_priority CHECK (priority_level >= 1 AND priority_level <= 5),
    CONSTRAINT valid_status CHECK (status IN ('draft', 'active', 'achieved', 'paused', 'cancelled'))
);

-- Enable RLS
ALTER TABLE savings_goals ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- BASIC INDEXES FOR PERFORMANCE
-- ============================================================================

-- Households
CREATE INDEX idx_households_created_at ON households(created_at DESC);

-- Users
CREATE INDEX idx_users_household_id ON users(household_id);
CREATE INDEX idx_users_email ON users(email);

-- Budget Periods
CREATE INDEX idx_budget_periods_household_id ON budget_periods(household_id);
CREATE INDEX idx_budget_periods_status ON budget_periods(status);
CREATE INDEX idx_budget_periods_dates ON budget_periods(period_start, period_end);

-- Income Sources
CREATE INDEX idx_income_sources_household_id ON income_sources(household_id);

-- Budget Categories
CREATE INDEX idx_budget_categories_period ON budget_categories(budget_period_id);
CREATE INDEX idx_budget_categories_type ON budget_categories(category_type);

-- Expenses (Hot table - optimized for frequent queries)
CREATE INDEX idx_expenses_category_date ON expenses(budget_category_id, expense_date DESC);
CREATE INDEX idx_expenses_user_date ON expenses(user_id, expense_date DESC);
CREATE INDEX idx_expenses_date_amount ON expenses(expense_date DESC, amount DESC);

-- Savings Goals
CREATE INDEX idx_savings_goals_household_id ON savings_goals(household_id);
CREATE INDEX idx_savings_goals_status ON savings_goals(status);

-- ============================================================================
-- ROW LEVEL SECURITY POLICIES
-- ============================================================================

-- Households: Users can only access their own household
CREATE POLICY households_access ON households
    FOR ALL USING (
        id IN (
            SELECT household_id FROM users WHERE id = auth.uid()
        )
    );

-- Users: Users can only access members of their household
CREATE POLICY users_access ON users
    FOR ALL USING (
        household_id IN (
            SELECT household_id FROM users WHERE id = auth.uid()
        )
    );

-- Budget Periods: Users can only access their household's budget periods
CREATE POLICY budget_periods_access ON budget_periods
    FOR ALL USING (
        household_id IN (
            SELECT household_id FROM users WHERE id = auth.uid()
        )
    );

-- Income Sources: Users can only access their household's income sources
CREATE POLICY income_sources_access ON income_sources
    FOR ALL USING (
        household_id IN (
            SELECT household_id FROM users WHERE id = auth.uid()
        )
    );

-- Budget Categories: Users can only access their household's budget categories
CREATE POLICY budget_categories_access ON budget_categories
    FOR ALL USING (
        budget_period_id IN (
            SELECT bp.id FROM budget_periods bp
            JOIN users u ON bp.household_id = u.household_id
            WHERE u.id = auth.uid()
        )
    );

-- Expenses: Users can only access their household's expenses
CREATE POLICY expenses_access ON expenses
    FOR ALL USING (
        budget_category_id IN (
            SELECT bc.id FROM budget_categories bc
            JOIN budget_periods bp ON bc.budget_period_id = bp.id
            JOIN users u ON bp.household_id = u.household_id
            WHERE u.id = auth.uid()
        )
    );

-- Savings Goals: Users can only access their household's savings goals
CREATE POLICY savings_goals_access ON savings_goals
    FOR ALL USING (
        household_id IN (
            SELECT household_id FROM users WHERE id = auth.uid()
        )
    );

-- ============================================================================
-- INSERT SYSTEM TEMPLATES
-- ============================================================================

-- Insert system templates
INSERT INTO category_templates (name, category_type, icon_name, color_theme, description, suggested_percentage, is_system_template) VALUES
-- NEEDS Categories
('Housing & Utilities', 'needs', 'House', 'needs', 'Rent, mortgage, electricity, gas, water, rates', 25.00, TRUE),
('Transport', 'needs', 'Car', 'needs', 'Car payments, fuel, public transport, maintenance', 10.00, TRUE),
('Groceries & Food', 'needs', 'ShoppingCart', 'needs', 'Supermarket shopping, essential food items', 8.00, TRUE),
('Healthcare', 'needs', 'Heart', 'needs', 'Medical expenses, insurance, prescriptions', 3.00, TRUE),
('Insurance', 'needs', 'Umbrella', 'needs', 'Health, car, home, life insurance premiums', 2.00, TRUE),
('Education', 'needs', 'GraduationCap', 'needs', 'School fees, courses, educational materials', 2.00, TRUE),

-- WANTS Categories
('Entertainment', 'wants', 'GameController', 'wants', 'Movies, games, streaming services, hobbies', 5.00, TRUE),
('Dining Out', 'wants', 'ForkKnife', 'wants', 'Restaurants, cafes, takeaway food', 3.00, TRUE),
('Personal Care', 'wants', 'Scissors', 'wants', 'Haircuts, beauty, clothing, accessories', 4.00, TRUE),
('Subscriptions', 'wants', 'Television', 'wants', 'Streaming, magazines, gym memberships', 2.00, TRUE),
('Shopping', 'wants', 'ShoppingBag', 'wants', 'Non-essential purchases, gadgets, home decor', 3.00, TRUE),
('Social Activities', 'wants', 'Users', 'wants', 'Socializing, events, recreational activities', 3.00, TRUE),

-- GOALS Categories
('Emergency Fund', 'goals', 'Umbrella', 'goals', '6-month expense safety net', 10.00, TRUE),
('House Deposit', 'goals', 'Buildings', 'goals', 'Saving for home purchase deposit', 5.00, TRUE),
('Investments', 'goals', 'TrendingUp', 'goals', 'Shares, ETFs, investment accounts', 5.00, TRUE),
('Retirement', 'goals', 'Armchair', 'goals', 'Superannuation, retirement savings', 3.00, TRUE),
('Car Replacement', 'goals', 'Car', 'goals', 'Saving for next vehicle purchase', 2.00, TRUE),

-- SMILE Categories
('Travel & Holidays', 'smile', 'Airplane', 'smile', 'Vacations, weekend trips, travel experiences', 3.00, TRUE),
('Family Experiences', 'smile', 'Balloon', 'smile', 'Activities, outings, special family moments', 1.00, TRUE),
('Treats & Surprises', 'smile', 'Gift', 'smile', 'Spontaneous purchases, gifts, indulgences', 1.00, TRUE);

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;