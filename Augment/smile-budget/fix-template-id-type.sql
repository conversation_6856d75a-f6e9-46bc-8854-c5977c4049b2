-- Fix template_id column type to accept string values instead of UUID
-- Run this in your Supabase SQL Editor

-- First, drop the foreign key constraint
ALTER TABLE public.budget_categories 
DROP CONSTRAINT IF EXISTS budget_categories_template_id_fkey;

-- Change template_id from UUID to VARCHAR to accept string category identifiers
ALTER TABLE public.budget_categories 
ALTER COLUMN template_id TYPE VARCHAR(50);

-- Since template_id is now for category template names (like "housing", "transport"), 
-- we don't need a foreign key constraint to another table