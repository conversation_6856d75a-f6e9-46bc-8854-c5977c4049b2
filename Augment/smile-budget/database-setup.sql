-- Smile Budget Database Schema
-- Run these commands in your Supabase SQL Editor

-- Note: auth.users table already has RLS enabled by Supabase

-- 1. Households table
CREATE TABLE IF NOT EXISTS public.households (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    currency_code TEXT DEFAULT 'AUD',
    timezone TEXT DEFAULT 'Australia/Sydney',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. User Profiles table (separate from auth.users to avoid RLS conflicts)
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    household_id UUID REFERENCES public.households(id) ON DELETE CASCADE,
    full_name TEXT NOT NULL,
    role TEXT DEFAULT 'member',
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Income Sources table
CREATE TABLE IF NOT EXISTS public.income_sources (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    household_id UUID REFERENCES public.households(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    source_type TEXT NOT NULL,
    frequency TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Budget Periods table
CREATE TABLE IF NOT EXISTS public.budget_periods (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    household_id UUID REFERENCES public.households(id) ON DELETE CASCADE,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    status TEXT DEFAULT 'active',
    total_planned_income DECIMAL(10,2) DEFAULT 0,
    total_actual_income DECIMAL(10,2) DEFAULT 0,
    needs_percentage INTEGER DEFAULT 50,
    wants_percentage INTEGER DEFAULT 20,
    goals_percentage INTEGER DEFAULT 25,
    smile_percentage INTEGER DEFAULT 5,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Budget Categories table
CREATE TABLE IF NOT EXISTS public.budget_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    budget_period_id UUID REFERENCES public.budget_periods(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    category_type TEXT NOT NULL,
    icon_name TEXT,
    planned_amount DECIMAL(10,2) DEFAULT 0,
    actual_amount DECIMAL(10,2) DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    template_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Expenses table
CREATE TABLE IF NOT EXISTS public.expenses (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    budget_category_id UUID REFERENCES public.budget_categories(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    description TEXT,
    expense_date DATE DEFAULT CURRENT_DATE,
    payment_method TEXT DEFAULT 'card',
    vendor_name TEXT,
    receipt_url TEXT,
    tags TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on all custom tables
ALTER TABLE public.households ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.income_sources ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.budget_periods ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.budget_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.expenses ENABLE ROW LEVEL SECURITY;

-- RLS Policies for households
CREATE POLICY "Users can view their own household" ON public.households
    FOR SELECT USING (
        id IN (
            SELECT household_id FROM public.user_profiles 
            WHERE id = auth.uid()
        )
    );

CREATE POLICY "Users can insert households" ON public.households
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update their own household" ON public.households
    FOR UPDATE USING (
        id IN (
            SELECT household_id FROM public.user_profiles 
            WHERE id = auth.uid()
        )
    );

-- RLS Policies for user_profiles
CREATE POLICY "Users can view profiles in their household" ON public.user_profiles
    FOR SELECT USING (
        household_id IN (
            SELECT household_id FROM public.user_profiles 
            WHERE id = auth.uid()
        )
    );

CREATE POLICY "Users can insert their own profile" ON public.user_profiles
    FOR INSERT WITH CHECK (id = auth.uid());

CREATE POLICY "Users can update their own profile" ON public.user_profiles
    FOR UPDATE USING (id = auth.uid());

-- RLS Policies for income_sources
CREATE POLICY "Users can view household income sources" ON public.income_sources
    FOR SELECT USING (
        household_id IN (
            SELECT household_id FROM public.user_profiles 
            WHERE id = auth.uid()
        )
    );

CREATE POLICY "Users can insert household income sources" ON public.income_sources
    FOR INSERT WITH CHECK (
        household_id IN (
            SELECT household_id FROM public.user_profiles 
            WHERE id = auth.uid()
        )
    );

CREATE POLICY "Users can update household income sources" ON public.income_sources
    FOR UPDATE USING (
        household_id IN (
            SELECT household_id FROM public.user_profiles 
            WHERE id = auth.uid()
        )
    );

-- RLS Policies for budget_periods
CREATE POLICY "Users can view household budget periods" ON public.budget_periods
    FOR SELECT USING (
        household_id IN (
            SELECT household_id FROM public.user_profiles 
            WHERE id = auth.uid()
        )
    );

CREATE POLICY "Users can insert household budget periods" ON public.budget_periods
    FOR INSERT WITH CHECK (
        household_id IN (
            SELECT household_id FROM public.user_profiles 
            WHERE id = auth.uid()
        )
    );

CREATE POLICY "Users can update household budget periods" ON public.budget_periods
    FOR UPDATE USING (
        household_id IN (
            SELECT household_id FROM public.user_profiles 
            WHERE id = auth.uid()
        )
    );

-- RLS Policies for budget_categories
CREATE POLICY "Users can view household budget categories" ON public.budget_categories
    FOR SELECT USING (
        budget_period_id IN (
            SELECT bp.id FROM public.budget_periods bp
            JOIN public.user_profiles up ON bp.household_id = up.household_id
            WHERE up.id = auth.uid()
        )
    );

CREATE POLICY "Users can insert household budget categories" ON public.budget_categories
    FOR INSERT WITH CHECK (
        budget_period_id IN (
            SELECT bp.id FROM public.budget_periods bp
            JOIN public.user_profiles up ON bp.household_id = up.household_id
            WHERE up.id = auth.uid()
        )
    );

CREATE POLICY "Users can update household budget categories" ON public.budget_categories
    FOR UPDATE USING (
        budget_period_id IN (
            SELECT bp.id FROM public.budget_periods bp
            JOIN public.user_profiles up ON bp.household_id = up.household_id
            WHERE up.id = auth.uid()
        )
    );

-- RLS Policies for expenses
CREATE POLICY "Users can view household expenses" ON public.expenses
    FOR SELECT USING (
        budget_category_id IN (
            SELECT bc.id FROM public.budget_categories bc
            JOIN public.budget_periods bp ON bc.budget_period_id = bp.id
            JOIN public.user_profiles up ON bp.household_id = up.household_id
            WHERE up.id = auth.uid()
        )
    );

CREATE POLICY "Users can insert their own expenses" ON public.expenses
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own expenses" ON public.expenses
    FOR UPDATE USING (user_id = auth.uid());

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER households_updated_at
    BEFORE UPDATE ON public.households
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER user_profiles_updated_at
    BEFORE UPDATE ON public.user_profiles
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER income_sources_updated_at
    BEFORE UPDATE ON public.income_sources
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER budget_periods_updated_at
    BEFORE UPDATE ON public.budget_periods
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER budget_categories_updated_at
    BEFORE UPDATE ON public.budget_categories
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER expenses_updated_at
    BEFORE UPDATE ON public.expenses
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_household_id ON public.user_profiles(household_id);
CREATE INDEX IF NOT EXISTS idx_income_sources_household_id ON public.income_sources(household_id);
CREATE INDEX IF NOT EXISTS idx_budget_periods_household_id ON public.budget_periods(household_id);
CREATE INDEX IF NOT EXISTS idx_budget_categories_budget_period_id ON public.budget_categories(budget_period_id);
CREATE INDEX IF NOT EXISTS idx_expenses_budget_category_id ON public.expenses(budget_category_id);
CREATE INDEX IF NOT EXISTS idx_expenses_user_id ON public.expenses(user_id);
CREATE INDEX IF NOT EXISTS idx_expenses_expense_date ON public.expenses(expense_date);