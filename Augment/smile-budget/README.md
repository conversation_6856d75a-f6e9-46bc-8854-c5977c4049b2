# Smile Budget 😊

A comprehensive family-focused budgeting app built with Next.js and Supabase, designed specifically for Australian families using the revolutionary "Smile Philosophy" - balancing essential needs, lifestyle wants, future goals, and most importantly, family happiness.

## 🚀 Quick Deploy

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/cctubemax/simpleBudget.git&env=NEXT_PUBLIC_SUPABASE_URL,NEXT_PUBLIC_SUPABASE_ANON_KEY&envDescription=Supabase%20credentials%20required&envLink=https://app.supabase.com/project/_/settings/api)

**Ready to deploy in under 5 minutes!** See [DEPLOYMENT.md](./DEPLOYMENT.md) for detailed instructions.

## ✨ Key Features

### **🎯 Complete User Experience**
- **Professional Landing Page**: Marketing-focused homepage with clear value proposition
- **Seamless Authentication**: Smart signup/signin flow with session management
- **6-Step Onboarding Wizard**: Intuitive setup for Australian families
- **Real-time Dashboard**: Interactive budget tracking and progress monitoring
- **Mobile-First Design**: Fully responsive across all devices

### **💰 Smile Philosophy Budgeting**
- **4-Category System**: Needs (50%), Wants (30%), Goals (15%), Smile (5%)
- **Australian-Focused**: Currency, terminology, and examples for Aussie families
- **Visual Budget Allocation**: Interactive percentage sliders and visual feedback
- **Category Selection**: 18+ pre-defined categories across all budget types
- **Custom Amount Assignment**: Flexible dollar allocation per category

### **🔐 Robust Authentication & Data**
- **Complete User Management**: Signup, signin, session persistence
- **Supabase Integration**: PostgreSQL database with Row Level Security
- **Email Verification Flow**: Progressive authentication with email verification
- **Data Persistence**: All onboarding data saved to database
- **Household Management**: Multi-user family account support

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL + Auth)
- **Testing**: Jest with React Testing Library
- **Deployment**: Vercel (recommended)
- **Styling**: Custom "Confident Optimism" design system

## 🏃‍♂️ Quick Start

### Prerequisites
- Node.js 18+ installed
- A Supabase account (free tier available)

### Local Development

1. **Clone the repository**:
   ```bash
   git clone https://github.com/cctubemax/simpleBudget.git
   cd simpleBudget
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env.local
   ```
   
   Edit `.env.local` with your Supabase credentials:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
   ```

4. **Set up the database**:
   - Go to your Supabase SQL Editor
   - Run the SQL files in order:
     1. `database-setup-simple.sql`
     2. `add-missing-columns.sql`
     3. `fix-template-id-type.sql`
     4. `fix-income-sources-amount.sql`

5. **Start the development server**:
   ```bash
   npm run dev
   ```

6. **Open your browser**:
   Visit [http://localhost:3005](http://localhost:3005)

## 🎉 What's New in Latest Version

### **🏠 Professional Landing Page**
- Marketing-focused homepage with clear value proposition
- Interactive Smile Philosophy explanation
- Smart authentication modals with form validation
- "See Demo First" option for user exploration
- Mobile-responsive design with smooth animations

### **🔐 Streamlined Authentication**
- Smart signup flow with session persistence
- Automatic signin after successful registration
- Email confirmation handling with development workarounds
- Pre-filled credentials for smoother UX
- Temporary user support for development testing

### **⚡ Enhanced Development Experience**
- Complete TypeScript integration with zero linting errors
- Robust error handling and logging throughout app
- Development debugging tools (SupabaseTest component)
- Comprehensive SQL migration scripts
- Production-ready deployment configuration

### **📱 Improved User Experience**
- Eliminated redundant authentication steps
- Smart button text based on user state
- Enhanced error messages and user feedback
- Seamless flow from landing → onboarding → dashboard
- No double data entry requirements

## 📱 Complete User Journey

### **🏠 Landing Page Experience**
- **Hero Section**: Compelling value proposition about financial happiness
- **Smile Philosophy Explanation**: Interactive 4-category breakdown
- **Benefits Showcase**: Australian family-focused messaging
- **Authentication Options**: Sign up or sign in buttons in header
- **Demo Access**: "See Demo First" option for curious users

### **The Revolutionary Smile Philosophy**
Our research-backed 4-category approach to family budgeting:

1. **🏠 NEEDS (50%)**: Essential expenses like housing, groceries, utilities, transport
2. **🎉 WANTS (30%)**: Lifestyle choices like dining out, entertainment, shopping
3. **🎯 GOALS (15%)**: Future planning like savings, investments, emergency fund  
4. **😊 SMILE (5%)**: Pure joy expenses like family treats, spontaneous fun, travel

### **Complete User Flow**

#### **New User Path:**
1. **Landing Page**: Learn about Smile Philosophy → Click "Get Started Free"
2. **Account Creation**: Sign up with email/password/name
3. **Onboarding Flow**: 6-step wizard
   - **Step 1**: Household setup (name, composition, pets)
   - **Step 2**: Income sources (multiple streams, frequencies)
   - **Step 3**: Budget allocation (percentage sliders)
   - **Step 4**: Category selection (18+ predefined options)
   - **Step 5**: Amount assignment (dollar allocation)
   - **Step 6**: Review & launch (automatic budget creation)
4. **Dashboard**: Real-time budget overview and tracking

#### **Returning User Path:**
1. **Landing Page**: Click "Sign In"
2. **Authentication**: Quick signin
3. **Dashboard**: Direct access to existing budget

#### **Demo User Path:**
1. **Landing Page**: Click "See Demo First"  
2. **Onboarding Demo**: Experience the full 6-step flow
3. **Account Creation**: Optional signup at the end

## 🔧 Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript checks

# Testing Scripts
npm test             # Run all tests
npm run test:watch   # Run tests in watch mode
npm run test:coverage # Run tests with coverage report
```

## 🧪 Testing

This project includes comprehensive unit testing with Jest and React Testing Library.

### **Testing Framework**
- **Jest**: JavaScript testing framework with TypeScript support
- **React Testing Library**: Testing utilities for React components
- **jsdom**: DOM environment for testing React components
- **Coverage Reporting**: Configured with 70% coverage thresholds

### **Test Structure**
```
src/
├── __tests__/           # Test files
├── test-utils/          # Testing utilities and mocks
│   └── index.tsx        # Mock data and helper functions
├── jest.config.js       # Jest configuration
└── jest.setup.js        # Global test setup
```

### **Running Tests**
```bash
# Run all tests once
npm test

# Run tests in watch mode (for development)
npm run test:watch

# Run tests with coverage report
npm run test:coverage
```

### **Test Coverage**
Coverage thresholds are set to 70% for:
- Statements
- Branches
- Functions
- Lines

View detailed coverage reports in the `coverage/` directory after running `npm run test:coverage`.

## 🚀 Production Deployment

### Option 1: Vercel (Recommended)
1. Click the "Deploy with Vercel" button above
2. Connect your GitHub account
3. Add your Supabase environment variables
4. Deploy!

### Option 2: Manual Deploy
See [DEPLOYMENT.md](./DEPLOYMENT.md) for detailed instructions for Vercel, Netlify, Railway, and other platforms.

## 📊 Database Architecture

### **Core Tables**
- **households**: Family units with currency (AUD) and timezone (Australia/Sydney)
- **user_profiles**: User accounts linked to households (separate from auth.users)
- **income_sources**: Multiple income streams with frequency and expected_amount
- **budget_periods**: Monthly budget cycles with percentage allocations
- **budget_categories**: Spending categories with allocated_amount and actual_amount
- **expenses**: Individual expense tracking (future feature)

### **Key Features**
- **Row Level Security (RLS)**: Temporarily disabled for smooth development
- **Foreign Key Relationships**: Proper data integrity across tables
- **TypeScript Types**: Full type safety with Supabase integration
- **Migration Scripts**: Complete SQL setup files included
- **Error Handling**: Comprehensive error logging and recovery

## 🎨 Design System

**Confident Optimism** design language featuring:
- **Primary**: Teal (encouraging financial growth)
- **Success**: Green (celebrating achievements)
- **Needs**: Blue (stability and security)
- **Wants**: Purple (lifestyle and desires)
- **Goals**: Orange (ambition and planning)
- **Smile**: Pink (joy and happiness)

## 🤝 Contributing

We welcome contributions! Please:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

MIT License - see [LICENSE](LICENSE) for details.

## 🆘 Support

- 📖 Read the [DEPLOYMENT.md](./DEPLOYMENT.md) guide
- 🐛 Report bugs via GitHub Issues
- 💬 Questions? Open a GitHub Discussion

## 🙏 Acknowledgments

Built with love for Australian families, powered by:
- [Next.js](https://nextjs.org/) - The React Framework
- [Supabase](https://supabase.com/) - The Open Source Firebase Alternative
- [Tailwind CSS](https://tailwindcss.com/) - A Utility-First CSS Framework

---

**Start your family's journey to financial happiness today!** 😊💰