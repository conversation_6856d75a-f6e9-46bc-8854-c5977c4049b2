-- ==========================================
-- STEP 4: Create Triggers and Indexes (Run last)
-- ==========================================

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply triggers to all tables
CREATE TRIGGER households_updated_at
    BEFORE UPDATE ON public.households
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER user_profiles_updated_at
    BEFORE UPDATE ON public.user_profiles
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER income_sources_updated_at
    BEFORE UPDATE ON public.income_sources
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER budget_periods_updated_at
    BEFORE UPDATE ON public.budget_periods
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER budget_categories_updated_at
    BEFORE UPDATE ON public.budget_categories
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER expenses_updated_at
    BEFORE UPDATE ON public.expenses
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_household_id ON public.user_profiles(household_id);
CREATE INDEX IF NOT EXISTS idx_income_sources_household_id ON public.income_sources(household_id);
CREATE INDEX IF NOT EXISTS idx_budget_periods_household_id ON public.budget_periods(household_id);
CREATE INDEX IF NOT EXISTS idx_budget_categories_budget_period_id ON public.budget_categories(budget_period_id);
CREATE INDEX IF NOT EXISTS idx_expenses_budget_category_id ON public.expenses(budget_category_id);
CREATE INDEX IF NOT EXISTS idx_expenses_user_id ON public.expenses(user_id);
CREATE INDEX IF NOT EXISTS idx_expenses_expense_date ON public.expenses(expense_date);