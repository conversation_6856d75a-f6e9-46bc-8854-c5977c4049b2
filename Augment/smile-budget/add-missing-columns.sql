-- Add missing columns to fix schema errors
-- Run this in your Supabase SQL Editor

-- Add all missing columns to budget_categories table
ALTER TABLE public.budget_categories 
ADD COLUMN IF NOT EXISTS planned_amount DECIMAL(12,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS actual_amount DECIMAL(12,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS template_id VARCHAR(50),
ADD COLUMN IF NOT EXISTS icon_name VARCHAR(50),
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;

-- Add missing columns to income_sources table  
ALTER TABLE public.income_sources 
ADD COLUMN IF NOT EXISTS source_type VARCHAR(50) DEFAULT 'salary',
ADD COLUMN IF NOT EXISTS frequency VARCHAR(20) DEFAULT 'monthly',
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;

-- Add missing columns to budget_periods table if needed
ALTER TABLE public.budget_periods 
ADD COLUMN IF NOT EXISTS total_planned_income DECIMAL(12,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS needs_percentage INTEGER DEFAULT 50,
ADD COLUMN IF NOT EXISTS wants_percentage INTEGER DEFAULT 30, 
ADD COLUMN IF NOT EXISTS goals_percentage INTEGER DEFAULT 15,
ADD COLUMN IF NOT EXISTS smile_percentage INTEGER DEFAULT 5,
ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'active';

-- Update existing records to have proper default values
UPDATE public.budget_categories 
SET 
  planned_amount = COALESCE(planned_amount, 0.00),
  actual_amount = COALESCE(actual_amount, 0.00),
  is_active = COALESCE(is_active, true)
WHERE planned_amount IS NULL OR actual_amount IS NULL OR is_active IS NULL;

UPDATE public.income_sources 
SET 
  source_type = COALESCE(source_type, 'salary'),
  frequency = COALESCE(frequency, 'monthly'),
  is_active = COALESCE(is_active, true)
WHERE source_type IS NULL OR frequency IS NULL OR is_active IS NULL;

UPDATE public.budget_periods 
SET 
  total_planned_income = COALESCE(total_planned_income, 0.00),
  needs_percentage = COALESCE(needs_percentage, 50),
  wants_percentage = COALESCE(wants_percentage, 30),
  goals_percentage = COALESCE(goals_percentage, 15),
  smile_percentage = COALESCE(smile_percentage, 5),
  status = COALESCE(status, 'active')
WHERE total_planned_income IS NULL OR status IS NULL;

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';