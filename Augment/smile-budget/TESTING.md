# Testing Guide

This document provides comprehensive information about testing in the Smile Budget application.

## 🧪 Testing Framework

### **Core Technologies**
- **Jest**: JavaScript testing framework with TypeScript support
- **React Testing Library**: Testing utilities for React components
- **jsdom**: DOM environment for testing React components
- **@testing-library/user-event**: User interaction simulation
- **@testing-library/jest-dom**: Custom Jest matchers for DOM testing

### **Configuration Files**
- `jest.config.js`: Main Jest configuration
- `jest.setup.js`: Global test setup and mocks
- `src/test-utils/index.tsx`: Testing utilities and mock data

## 🏗️ Test Structure

```
src/
├── __tests__/           # Test files
│   └── setup.test.ts    # Basic configuration tests
├── test-utils/          # Testing utilities
│   └── index.tsx        # Mock data and helper functions
├── jest.config.js       # Jest configuration
└── jest.setup.js        # Global test setup
```

## 🚀 Running Tests

### **Available Commands**
```bash
# Run all tests once
npm test

# Run tests in watch mode (for development)
npm run test:watch

# Run tests with coverage report
npm run test:coverage
```

### **Test Output**
- Tests run in the terminal with colored output
- Coverage reports are generated in the `coverage/` directory
- HTML coverage reports available at `coverage/lcov-report/index.html`

## 📊 Coverage Requirements

### **Thresholds**
All coverage metrics must meet 70% minimum:
- **Statements**: 70%
- **Branches**: 70%
- **Functions**: 70%
- **Lines**: 70%

### **Coverage Reports**
- **Terminal**: Summary displayed after running `npm run test:coverage`
- **HTML**: Detailed report in `coverage/lcov-report/index.html`
- **LCOV**: Machine-readable format in `coverage/lcov.info`
- **JSON**: Raw data in `coverage/coverage-final.json`

## 🛠️ Test Utilities

### **Mock Data**
Located in `src/test-utils/index.tsx`:

```typescript
// User mocks
export const mockUser = { id: 'user-123', email: '<EMAIL>', ... }
export const mockPendingUser = { id: 'pending-123', email: '<EMAIL>', ... }

// Data mocks
export const mockHousehold = { id: 'household-123', monthly_income: 5000, ... }
export const mockBudgetSummary = { needs: 2500, wants: 1500, goals: 750, smile: 250 }

// Supabase response mocks
export const mockSupabaseResponses = {
  signUpSuccess: { data: { user: mockUser, session: null }, error: null },
  signInSuccess: { data: { user: mockUser, session: mockSession }, error: null },
  // ... more responses
}
```

### **Helper Functions**
```typescript
// Mock localStorage
export const mockLocalStorage = () => ({ getItem: jest.fn(), setItem: jest.fn(), ... })

// Mock Supabase methods
export const mockSupabaseMethod = (method: any, response: any) => {
  method.mockResolvedValue(response)
}
```

## 🎯 Testing Strategy

### **Phase 1: Foundation (Complete)**
- ✅ Jest configuration and setup
- ✅ Test utilities and mock data
- ✅ Basic configuration tests

### **Phase 2: Core Services (In Progress)**
- 🔄 AuthService unit tests
- 🔄 DatabaseService unit tests
- 🔄 Utility function tests

### **Phase 3: Components (Planned)**
- ⏳ Landing page component tests
- ⏳ Onboarding component tests
- ⏳ Dashboard component tests
- ⏳ Email verification tests

### **Phase 4: Integration (Planned)**
- ⏳ End-to-end user flow tests
- ⏳ Authentication flow tests
- ⏳ Data persistence tests

## 🔧 Configuration Details

### **Jest Configuration**
```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/test-utils/**',
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
  },
}
```

### **Global Mocks**
```javascript
// jest.setup.js
import '@testing-library/jest-dom'

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
}))

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
global.localStorage = localStorageMock

// Mock console methods to reduce test noise
global.console = {
  ...console,
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
}
```

## 📝 Writing Tests

### **Test File Naming**
- Test files: `*.test.ts` or `*.test.tsx`
- Location: `src/__tests__/` directory
- Naming: Match the file being tested (e.g., `AuthService.test.ts` for `lib/database.ts`)

### **Test Structure**
```typescript
import { mockUser, mockSupabaseResponses } from '@/test-utils'

describe('AuthService', () => {
  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks()
  })

  describe('signup', () => {
    it('should create a new user account', async () => {
      // Arrange
      const mockSignUp = jest.fn()
      mockSupabaseMethod(mockSignUp, mockSupabaseResponses.signUpSuccess)

      // Act
      const result = await AuthService.signup('<EMAIL>', 'password', 'Test User')

      // Assert
      expect(result.success).toBe(true)
      expect(result.user).toEqual(mockUser)
    })
  })
})
```

## 🐛 Troubleshooting

### **Common Issues**

1. **Module Resolution Errors**
   - Ensure `moduleNameMapper` is correctly configured in `jest.config.js`
   - Check that `@/` paths resolve to `src/` directory

2. **Supabase Mock Issues**
   - Import Supabase mocks in individual test files, not globally
   - Use `mockSupabaseMethod` helper for consistent mocking

3. **React Testing Library Errors**
   - Don't import React Testing Library in test utilities
   - Import directly in test files to avoid hook issues

4. **Coverage Threshold Failures**
   - Check which files are not meeting 70% coverage
   - Add tests for uncovered code paths
   - Update thresholds if necessary for specific files

### **Debug Tips**
```bash
# Run specific test file
npm test -- AuthService.test.ts

# Run tests with verbose output
npm test -- --verbose

# Run tests without coverage
npm test -- --coverage=false

# Update snapshots
npm test -- --updateSnapshot
```

## 🎯 Best Practices

1. **Test Organization**: Group related tests in `describe` blocks
2. **Mock Management**: Reset mocks in `beforeEach` hooks
3. **Assertions**: Use specific matchers from `@testing-library/jest-dom`
4. **User Interactions**: Use `@testing-library/user-event` for realistic interactions
5. **Async Testing**: Always `await` async operations
6. **Error Testing**: Test both success and error scenarios
7. **Coverage**: Aim for meaningful tests, not just coverage numbers

## 📚 Resources

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Testing Library Best Practices](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library)
- [Jest DOM Matchers](https://github.com/testing-library/jest-dom#custom-matchers)
