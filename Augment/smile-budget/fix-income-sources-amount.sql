-- Add missing amount column to income_sources table
-- Run this in your Supabase SQL Editor

-- Add the amount column to income_sources
ALTER TABLE public.income_sources 
ADD COLUMN IF NOT EXISTS amount DECIMAL(12,2) DEFAULT 0.00;

-- Update existing records to have default amount
UPDATE public.income_sources 
SET amount = COALESCE(amount, 0.00)
WHERE amount IS NULL;

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';