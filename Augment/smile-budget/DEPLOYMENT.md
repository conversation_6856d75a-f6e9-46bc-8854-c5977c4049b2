# Smile Budget - Production Deployment Guide

## Quick Deploy to Vercel (Recommended)

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/cctubemax/simpleBudget.git&env=NEXT_PUBLIC_SUPABASE_URL,NEXT_PUBLIC_SUPABASE_ANON_KEY&envDescription=Supabase%20credentials%20required&envLink=https://app.supabase.com/project/_/settings/api)

## Prerequisites

1. **Supabase Account**: Sign up at [supabase.com](https://supabase.com)
2. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
3. **GitHub Repository**: Fork or clone this repository

## Step-by-Step Deployment

### 1. Set Up Supabase Database

1. **Create a new Supabase project**:
   - Go to [app.supabase.com](https://app.supabase.com)
   - Click "New Project"
   - Choose organization and name your project
   - Set a database password and region

2. **Run the database setup**:
   - Go to SQL Editor in your Supabase dashboard
   - Run the SQL files in this order:
     ```
     1. database-setup-simple.sql    (creates all tables)
     2. add-missing-columns.sql      (adds missing columns)
     3. fix-template-id-type.sql     (fixes data types)
     4. fix-income-sources-amount.sql (adds amount column)
     ```

3. **Get your Supabase credentials**:
   - Go to Project Settings → API
   - Copy your `Project URL` and `anon/public key`

### 2. Deploy to Vercel

#### Option A: One-Click Deploy
1. Click the "Deploy with Vercel" button above
2. Connect your GitHub account
3. Add environment variables when prompted
4. Deploy!

#### Option B: Manual Deploy
1. **Connect GitHub to Vercel**:
   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your GitHub repository

2. **Configure Environment Variables**:
   ```
   NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

3. **Deploy**:
   - Click "Deploy"
   - Wait for build to complete
   - Your app will be live at `https://your-app-name.vercel.app`

### 3. Configure Authentication (Important!)

1. **Add your production URL to Supabase Auth**:
   - Go to Authentication → Settings in Supabase
   - Add your Vercel URL to "Site URL": `https://your-app.vercel.app`
   - Add your Vercel URL to "Redirect URLs": `https://your-app.vercel.app/**`

2. **Configure Email Verification**:
   - In Supabase: Authentication → Settings
   - **Keep "Enable email confirmations" CHECKED** (our new flow handles this properly)
   - Set "Site URL" to your production domain: `https://your-app.vercel.app`
   - Add redirect URL: `https://your-app.vercel.app/verify-email`
   - This provides secure email verification with smooth user experience

3. **Test your deployment**:
   - Visit your live app
   - Try the complete user flow: Landing page → Sign up → Onboarding → Dashboard
   - Verify data persists to Supabase database

## Alternative Deployment Options

### Netlify
1. Connect your GitHub repo to Netlify
2. Set build command: `npm run build`
3. Set publish directory: `.next`
4. Add environment variables
5. Deploy

### Railway
1. Connect GitHub repo to Railway
2. Add environment variables
3. Railway will auto-deploy

### AWS Amplify
1. Connect GitHub repo to Amplify
2. Configure build settings
3. Add environment variables
4. Deploy

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `NEXT_PUBLIC_SUPABASE_URL` | Your Supabase project URL | Yes |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | Your Supabase anonymous key | Yes |
| `SUPABASE_SERVICE_ROLE_KEY` | Service role key for server operations | No |
| `NEXT_PUBLIC_SITE_URL` | Your production URL | No (auto-set by Vercel) |

## Post-Deployment Checklist

### **Database & Backend**
- [ ] All Supabase database tables created (run SQL migration files)
- [ ] Environment variables configured correctly
- [ ] Email confirmation disabled for smooth UX
- [ ] Authentication URLs configured for production domain

### **User Experience Testing**
- [ ] Landing page loads and displays correctly
- [ ] Sign up flow works from landing page
- [ ] Sign in flow works for existing users  
- [ ] Complete 6-step onboarding flow functions
- [ ] Data persists to Supabase database
- [ ] Dashboard displays budget data correctly
- [ ] Authentication sessions persist properly

### **Technical Requirements**
- [ ] Mobile responsive design works on all devices
- [ ] SSL certificate active and working
- [ ] Page load speeds acceptable
- [ ] No console errors or TypeScript issues
- [ ] Custom domain configured (optional)

### **Production Optimizations**
- [ ] Vercel Analytics enabled
- [ ] Error tracking configured (optional)
- [ ] Performance monitoring setup
- [ ] SEO metadata configured

## Troubleshooting

### Common Issues

1. **"Invalid API key" error**:
   - Check your Supabase URL and anon key
   - Ensure no extra spaces in environment variables

2. **Authentication redirects fail**:
   - Add your production URL to Supabase Auth settings
   - Check Site URL and Redirect URLs

3. **Database errors**:
   - Verify all SQL migration files were run
   - Check Supabase logs for detailed errors

4. **Build fails**:
   - Check TypeScript errors: `npm run lint`
   - Verify all dependencies: `npm install`

### Support

If you encounter issues:
1. Check Vercel deployment logs
2. Check Supabase logs
3. Open an issue on GitHub

## Performance Optimization

- Enable Vercel Analytics
- Configure caching headers
- Optimize images with Next.js Image component
- Enable gzip compression
- Monitor Core Web Vitals

Your Smile Budget app should now be live and ready for users! 🎉