{"name": "smile-budget", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@supabase/supabase-js": "^2.50.0", "@tanstack/react-query": "^5.80.7", "next": "15.3.3", "phosphor-react": "^1.4.1", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^2.15.3", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}