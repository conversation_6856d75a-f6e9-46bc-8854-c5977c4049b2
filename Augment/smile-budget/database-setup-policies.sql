-- ==========================================
-- STEP 3: Create RLS Policies (Run after Step 2)
-- ==========================================

-- RLS Policies for households
CREATE POLICY "Users can view their own household" ON public.households
    FOR SELECT USING (
        id IN (
            SELECT household_id FROM public.user_profiles 
            WHERE id = auth.uid()
        )
    );

CREATE POLICY "Users can insert households" ON public.households
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update their own household" ON public.households
    FOR UPDATE USING (
        id IN (
            SELECT household_id FROM public.user_profiles 
            WHERE id = auth.uid()
        )
    );

-- RLS Policies for user_profiles
CREATE POLICY "Users can view profiles in their household" ON public.user_profiles
    FOR SELECT USING (
        household_id IN (
            SELECT household_id FROM public.user_profiles 
            WHERE id = auth.uid()
        )
    );

CREATE POLICY "Users can insert their own profile" ON public.user_profiles
    FOR INSERT WITH CHECK (id = auth.uid());

CREATE POLICY "Users can update their own profile" ON public.user_profiles
    FOR UPDATE USING (id = auth.uid());

-- RLS Policies for income_sources
CREATE POLICY "Users can view household income sources" ON public.income_sources
    FOR SELECT USING (
        household_id IN (
            SELECT household_id FROM public.user_profiles 
            WHERE id = auth.uid()
        )
    );

CREATE POLICY "Users can insert household income sources" ON public.income_sources
    FOR INSERT WITH CHECK (
        household_id IN (
            SELECT household_id FROM public.user_profiles 
            WHERE id = auth.uid()
        )
    );

CREATE POLICY "Users can update household income sources" ON public.income_sources
    FOR UPDATE USING (
        household_id IN (
            SELECT household_id FROM public.user_profiles 
            WHERE id = auth.uid()
        )
    );

-- RLS Policies for budget_periods
CREATE POLICY "Users can view household budget periods" ON public.budget_periods
    FOR SELECT USING (
        household_id IN (
            SELECT household_id FROM public.user_profiles 
            WHERE id = auth.uid()
        )
    );

CREATE POLICY "Users can insert household budget periods" ON public.budget_periods
    FOR INSERT WITH CHECK (
        household_id IN (
            SELECT household_id FROM public.user_profiles 
            WHERE id = auth.uid()
        )
    );

CREATE POLICY "Users can update household budget periods" ON public.budget_periods
    FOR UPDATE USING (
        household_id IN (
            SELECT household_id FROM public.user_profiles 
            WHERE id = auth.uid()
        )
    );

-- RLS Policies for budget_categories
CREATE POLICY "Users can view household budget categories" ON public.budget_categories
    FOR SELECT USING (
        budget_period_id IN (
            SELECT bp.id FROM public.budget_periods bp
            JOIN public.user_profiles up ON bp.household_id = up.household_id
            WHERE up.id = auth.uid()
        )
    );

CREATE POLICY "Users can insert household budget categories" ON public.budget_categories
    FOR INSERT WITH CHECK (
        budget_period_id IN (
            SELECT bp.id FROM public.budget_periods bp
            JOIN public.user_profiles up ON bp.household_id = up.household_id
            WHERE up.id = auth.uid()
        )
    );

CREATE POLICY "Users can update household budget categories" ON public.budget_categories
    FOR UPDATE USING (
        budget_period_id IN (
            SELECT bp.id FROM public.budget_periods bp
            JOIN public.user_profiles up ON bp.household_id = up.household_id
            WHERE up.id = auth.uid()
        )
    );

-- RLS Policies for expenses
CREATE POLICY "Users can view household expenses" ON public.expenses
    FOR SELECT USING (
        budget_category_id IN (
            SELECT bc.id FROM public.budget_categories bc
            JOIN public.budget_periods bp ON bc.budget_period_id = bp.id
            JOIN public.user_profiles up ON bp.household_id = up.household_id
            WHERE up.id = auth.uid()
        )
    );

CREATE POLICY "Users can insert their own expenses" ON public.expenses
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own expenses" ON public.expenses
    FOR UPDATE USING (user_id = auth.uid());