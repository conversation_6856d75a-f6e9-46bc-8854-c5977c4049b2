import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key'

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
})

// Types for our database schema
export interface Database {
  public: {
    Tables: {
      households: {
        Row: {
          id: string
          name: string
          currency_code: string
          timezone: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          currency_code?: string
          timezone?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          currency_code?: string
          timezone?: string
          created_at?: string
          updated_at?: string
        }
      }
      user_profiles: {
        Row: {
          id: string
          household_id: string
          full_name: string
          role: string
          avatar_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          household_id: string
          full_name: string
          role?: string
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          household_id?: string
          full_name?: string
          role?: string
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      budget_periods: {
        Row: {
          id: string
          household_id: string
          period_start: string
          period_end: string
          status: string
          total_planned_income: number
          total_actual_income: number
          needs_percentage: number
          wants_percentage: number
          goals_percentage: number
          smile_percentage: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          household_id: string
          period_start: string
          period_end: string
          status?: string
          total_planned_income?: number
          total_actual_income?: number
          needs_percentage?: number
          wants_percentage?: number
          goals_percentage?: number
          smile_percentage?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          household_id?: string
          period_start?: string
          period_end?: string
          status?: string
          total_planned_income?: number
          total_actual_income?: number
          needs_percentage?: number
          wants_percentage?: number
          goals_percentage?: number
          smile_percentage?: number
          created_at?: string
          updated_at?: string
        }
      }
      income_sources: {
        Row: {
          id: string
          household_id: string
          name: string
          source_type: string
          frequency: string
          expected_amount: number
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          household_id: string
          name: string
          source_type: string
          frequency: string
          expected_amount: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          household_id?: string
          name?: string
          source_type?: string
          frequency?: string
          expected_amount?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      budget_categories: {
        Row: {
          id: string
          budget_period_id: string
          name: string
          category_type: string
          icon_name: string
          allocated_amount: number
          actual_amount: number
          is_active: boolean
          template_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          budget_period_id: string
          name: string
          category_type: string
          icon_name?: string
          allocated_amount?: number
          actual_amount?: number
          is_active?: boolean
          template_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          budget_period_id?: string
          name?: string
          category_type?: string
          icon_name?: string
          allocated_amount?: number
          actual_amount?: number
          is_active?: boolean
          template_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      expenses: {
        Row: {
          id: string
          budget_category_id: string
          user_id: string
          amount: number
          description: string | null
          expense_date: string
          payment_method: string
          vendor_name: string | null
          receipt_url: string | null
          tags: string[] | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          budget_category_id: string
          user_id: string
          amount: number
          description?: string | null
          expense_date?: string
          payment_method?: string
          vendor_name?: string | null
          receipt_url?: string | null
          tags?: string[] | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          budget_category_id?: string
          user_id?: string
          amount?: number
          description?: string | null
          expense_date?: string
          payment_method?: string
          vendor_name?: string | null
          receipt_url?: string | null
          tags?: string[] | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}

// Export a typed client
export type SupabaseClient = typeof supabase