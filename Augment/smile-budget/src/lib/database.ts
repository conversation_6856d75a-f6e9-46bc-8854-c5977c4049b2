import { supabase } from './supabase'
import { OnboardingData } from '@/app/onboarding/page'

// Database service for handling all onboarding and budget operations
export class DatabaseService {
  
  /**
   * Create a new household and user from onboarding data
   */
  static async createHouseholdFromOnboarding(
    onboardingData: OnboardingData,
    userEmail: string,
    userId?: string
  ) {
    try {
      console.log('Starting household creation with data:', {
        householdName: onboardingData.householdName,
        yourName: onboardingData.yourName,
        userEmail,
        userId,
        totalIncome: onboardingData.totalMonthlyIncome
      })
      // 1. Create household
      console.log('Creating household...')
      const { data: household, error: householdError } = await supabase
        .from('households')
        .insert({
          name: onboardingData.householdName,
          currency_code: 'AUD',
          timezone: 'Australia/Sydney'
        })
        .select()
        .single()

      console.log('Household creation result:', { household, householdError })
      if (householdError) {
        console.error('Household creation failed:', {
          error: householdError,
          message: householdError.message,
          details: householdError.details,
          hint: householdError.hint,
          code: householdError.code
        })
        throw new Error(`Failed to create household: ${householdError.message || householdError.code || 'Unknown database error'}`)
      }

      // 2. Create user profile (separate from auth.users)
      const { data: user, error: userError } = await supabase
        .from('user_profiles')
        .upsert({
          id: userId,
          household_id: household.id,
          full_name: onboardingData.yourName,
          role: 'admin'
        })
        .select()
        .single()

      if (userError) throw userError

      // 3. Create income sources
      const incomePromises = onboardingData.incomeSourcess.map(income => 
        supabase.from('income_sources').insert({
          household_id: household.id,
          name: income.name,
          source_type: income.type,
          frequency: income.frequency,
          expected_amount: income.amount,
          is_active: true
        })
      )

      await Promise.all(incomePromises)

      // 4. Create initial budget period
      const { data: budgetPeriod, error: budgetError } = await supabase
        .from('budget_periods')
        .insert({
          household_id: household.id,
          period_start: new Date().toISOString().split('T')[0],
          period_end: new Date(new Date().setMonth(new Date().getMonth() + 1)).toISOString().split('T')[0],
          status: 'active',
          total_planned_income: onboardingData.totalMonthlyIncome,
          needs_percentage: onboardingData.allocations.needs,
          wants_percentage: onboardingData.allocations.wants,
          goals_percentage: onboardingData.allocations.goals,
          smile_percentage: onboardingData.allocations.smile
        })
        .select()
        .single()

      if (budgetError) throw budgetError

      // 5. Create budget categories
      await this.createBudgetCategories(budgetPeriod.id, onboardingData)

      return {
        household,
        user,
        budgetPeriod,
        success: true
      }
    } catch (error) {
      // More robust error logging
      console.error('=== HOUSEHOLD CREATION ERROR ===')
      console.error('Error type:', typeof error)
      console.error('Error constructor:', error?.constructor?.name)
      console.error('Error object:', error)
      
      if (error instanceof Error) {
        console.error('Error message:', error.message)
        console.error('Error stack:', error.stack)
      }
      
      // Try to extract error details
      let errorMessage = 'Unknown database error'
      try {
        if (error instanceof Error) {
          errorMessage = error.message
        } else if (error && typeof error === 'object') {
          const errorObj = error as Record<string, unknown>
          errorMessage = (errorObj.message as string) || ((errorObj.error as Record<string, unknown>)?.message as string) || JSON.stringify(error)
        } else {
          errorMessage = String(error)
        }
      } catch (stringifyError) {
        console.error('Error stringifying error:', stringifyError)
        errorMessage = 'Error occurred but could not be serialized'
      }
      
      console.error('Final error message:', errorMessage)
      throw new Error(`Database operation failed: ${errorMessage}`)
    }
  }

  /**
   * Create budget categories for each selected category
   */
  static async createBudgetCategories(budgetPeriodId: string, onboardingData: OnboardingData) {
    const categoryTemplates = [
      // NEEDS Categories
      { id: 'housing', name: 'Housing & Utilities', category_type: 'needs', icon_name: '🏠' },
      { id: 'transport', name: 'Transport', category_type: 'needs', icon_name: '🚗' },
      { id: 'groceries', name: 'Groceries & Food', category_type: 'needs', icon_name: '🛒' },
      { id: 'healthcare', name: 'Healthcare', category_type: 'needs', icon_name: '❤️' },
      { id: 'insurance', name: 'Insurance', category_type: 'needs', icon_name: '☂️' },
      { id: 'education', name: 'Education', category_type: 'needs', icon_name: '🎓' },
      
      // WANTS Categories  
      { id: 'entertainment', name: 'Entertainment', category_type: 'wants', icon_name: '🎮' },
      { id: 'dining', name: 'Dining Out', category_type: 'wants', icon_name: '🍽️' },
      { id: 'personal', name: 'Personal Care', category_type: 'wants', icon_name: '✂️' },
      { id: 'subscriptions', name: 'Subscriptions', category_type: 'wants', icon_name: '📺' },
      { id: 'shopping', name: 'Shopping', category_type: 'wants', icon_name: '🛍️' },
      { id: 'social', name: 'Social Activities', category_type: 'wants', icon_name: '👥' },
      
      // GOALS Categories
      { id: 'emergency', name: 'Emergency Fund', category_type: 'goals', icon_name: '☂️' },
      { id: 'house_deposit', name: 'House Deposit', category_type: 'goals', icon_name: '🏢' },
      { id: 'investments', name: 'Investments', category_type: 'goals', icon_name: '📈' },
      { id: 'retirement', name: 'Retirement', category_type: 'goals', icon_name: '🪑' },
      { id: 'car_replacement', name: 'Car Replacement', category_type: 'goals', icon_name: '🚗' },
      
      // SMILE Categories
      { id: 'travel', name: 'Travel & Holidays', category_type: 'smile', icon_name: '✈️' },
      { id: 'family_experiences', name: 'Family Experiences', category_type: 'smile', icon_name: '🎈' },
      { id: 'treats', name: 'Treats & Surprises', category_type: 'smile', icon_name: '🎁' }
    ]

    const budgetCategories = []

    // Process each category type
    for (const [, selectedIds] of Object.entries(onboardingData.selectedCategories)) {
      for (const categoryId of selectedIds) {
        const template = categoryTemplates.find(t => t.id === categoryId)
        if (!template) continue

        // Find the amount for this category
        const categoryAmount = onboardingData.categoryAmounts?.find(ca => ca.categoryId === categoryId)
        const plannedAmount = categoryAmount?.amount || 0

        budgetCategories.push({
          budget_period_id: budgetPeriodId,
          name: template.name,
          category_type: template.category_type as 'needs' | 'wants' | 'goals' | 'smile',
          icon_name: template.icon_name,
          allocated_amount: plannedAmount,
          actual_amount: 0,
          is_active: true,
          template_id: categoryId
        })
      }
    }

    if (budgetCategories.length > 0) {
      const { error } = await supabase
        .from('budget_categories')
        .insert(budgetCategories)

      if (error) throw error
    }

    return budgetCategories
  }

  /**
   * Get household data with current budget period
   */
  static async getHouseholdData(householdId: string) {
    try {
      // Get household
      const { data: household, error: householdError } = await supabase
        .from('households')
        .select('*')
        .eq('id', householdId)
        .single()

      if (householdError) throw householdError

      // Get current budget period with categories
      const { data: budgetPeriod, error: budgetError } = await supabase
        .from('budget_periods')
        .select(`
          *,
          budget_categories (*)
        `)
        .eq('household_id', householdId)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .limit(1)
        .single()

      if (budgetError) throw budgetError

      // Get income sources
      const { data: incomeSources, error: incomeError } = await supabase
        .from('income_sources')
        .select('*')
        .eq('household_id', householdId)
        .eq('is_active', true)

      if (incomeError) throw incomeError

      // Get recent expenses for this budget period
      const { data: expenses, error: expensesError } = await supabase
        .from('expenses')
        .select(`
          *,
          budget_categories (name, category_type, icon_name)
        `)
        .in('budget_category_id', (budgetPeriod as Record<string, unknown>).budget_categories as string[])
        .gte('expense_date', budgetPeriod.period_start)
        .lte('expense_date', budgetPeriod.period_end)
        .order('expense_date', { ascending: false })

      if (expensesError) throw expensesError

      return {
        household,
        budgetPeriod,
        incomeSources,
        expenses: expenses || [],
        success: true
      }
    } catch (error) {
      console.error('Error getting household data:', error)
      throw error
    }
  }

  /**
   * Add a new expense
   */
  static async addExpense(
    budgetCategoryId: string,
    userId: string,
    amount: number,
    description?: string,
    expenseDate?: string,
    paymentMethod = 'card',
    vendorName?: string
  ) {
    try {
      const { data: expense, error } = await supabase
        .from('expenses')
        .insert({
          budget_category_id: budgetCategoryId,
          user_id: userId,
          amount,
          description,
          expense_date: expenseDate || new Date().toISOString().split('T')[0],
          payment_method: paymentMethod,
          vendor_name: vendorName
        })
        .select(`
          *,
          budget_categories (name, category_type, icon_name)
        `)
        .single()

      if (error) throw error

      // Update the budget category actual amount
      await this.updateCategoryActualAmount(budgetCategoryId)

      return { expense, success: true }
    } catch (error) {
      console.error('Error adding expense:', error)
      throw error
    }
  }

  /**
   * Update the actual amount for a budget category based on expenses
   */
  static async updateCategoryActualAmount(budgetCategoryId: string) {
    try {
      // Get the budget period date range
      const { data: category, error: categoryError } = await supabase
        .from('budget_categories')
        .select(`
          *,
          budget_periods (period_start, period_end)
        `)
        .eq('id', budgetCategoryId)
        .single()

      if (categoryError) throw categoryError

      // Calculate total expenses for this category in the current period
      const { data: expenses, error: expensesError } = await supabase
        .from('expenses')
        .select('amount')
        .eq('budget_category_id', budgetCategoryId)
        .gte('expense_date', ((category as Record<string, unknown>).budget_periods as Record<string, string>).period_start)
        .lte('expense_date', ((category as Record<string, unknown>).budget_periods as Record<string, string>).period_end)

      if (expensesError) throw expensesError

      const totalActual = expenses?.reduce((sum, expense) => sum + expense.amount, 0) || 0

      // Update the category
      const { error: updateError } = await supabase
        .from('budget_categories')
        .update({ actual_amount: totalActual })
        .eq('id', budgetCategoryId)

      if (updateError) throw updateError

      return { success: true, actualAmount: totalActual }
    } catch (error) {
      console.error('Error updating category actual amount:', error)
      throw error
    }
  }

  /**
   * Get budget summary for dashboard
   */
  static async getBudgetSummary(householdId: string) {
    try {
      const { data: budgetPeriod, error } = await supabase
        .from('budget_periods')
        .select(`
          *,
          budget_categories (
            id,
            name,
            category_type,
            icon_name,
            allocated_amount,
            actual_amount
          )
        `)
        .eq('household_id', householdId)
        .eq('status', 'active')
        .single()

      if (error) throw error

      // Group categories by type
      interface CategoryData {
        id: string
        name: string
        category_type: 'needs' | 'wants' | 'goals' | 'smile'
        icon_name: string
        allocated_amount: number
        actual_amount: number
      }
      
      const categories = ((budgetPeriod as Record<string, unknown>).budget_categories as CategoryData[]) || []
      const summary = {
        needs: { planned: 0, actual: 0, categories: [] as CategoryData[] },
        wants: { planned: 0, actual: 0, categories: [] as CategoryData[] },
        goals: { planned: 0, actual: 0, categories: [] as CategoryData[] },
        smile: { planned: 0, actual: 0, categories: [] as CategoryData[] }
      }

      categories.forEach((cat: CategoryData) => {
        const type = cat.category_type as keyof typeof summary
        summary[type].planned += cat.allocated_amount
        summary[type].actual += cat.actual_amount
        summary[type].categories.push(cat)
      })

      return {
        budgetPeriod,
        summary,
        totalPlanned: budgetPeriod.total_planned_income,
        totalSpent: Object.values(summary).reduce((sum, type) => sum + type.actual, 0),
        success: true
      }
    } catch (error) {
      console.error('Error getting budget summary:', error)
      throw error
    }
  }
}

// Auth helper functions
export class AuthService {
  
  /**
   * Sign up a new user and automatically sign them in
   */
  static async signUp(email: string, password: string, fullName: string) {
    try {
      // First, sign up the user
      const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName
          },
          emailRedirectTo: undefined // Disable email confirmation for smoother UX
        }
      })

      if (signUpError) throw signUpError

      // If the user was created but not automatically signed in (due to email confirmation)
      // Return the user anyway and let them continue without full authentication
      if (signUpData.user && !signUpData.session) {
        console.log('User created but email confirmation required')
        
        // Try to sign them in, but don't fail if email confirmation is required
        try {
          const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
            email,
            password
          })
          
          if (signInError && signInError.message !== 'Email not confirmed') {
            throw signInError
          }
          
          if (signInData?.session) {
            return { user: signInData.user, session: signInData.session, success: true }
          }
        } catch (signInError) {
          console.log('Sign in failed, probably due to email confirmation:', signInError)
        }
        
        // Return the signup data even if signin failed due to email confirmation
        return { user: signUpData.user, session: null, success: true, emailConfirmationRequired: true }
      }

      return { user: signUpData.user, session: signUpData.session, success: true }
    } catch (error) {
      console.error('Error signing up:', error)
      throw error
    }
  }

  /**
   * Sign in an existing user
   */
  static async signIn(email: string, password: string) {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) throw error

      return { user: data.user, session: data.session, success: true }
    } catch (error) {
      console.error('Error signing in:', error)
      throw error
    }
  }

  /**
   * Sign out current user
   */
  static async signOut() {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error
      return { success: true }
    } catch (error) {
      console.error('Error signing out:', error)
      throw error
    }
  }

  /**
   * Get current user session
   */
  static async getCurrentUser() {
    try {
      const { data: { session }, error } = await supabase.auth.getSession()
      if (error) throw error
      return { session, user: session?.user || null }
    } catch (error) {
      console.error('Error getting current user:', error)
      return { session: null, user: null }
    }
  }

  /**
   * Get user with household data
   */
  static async getUserWithHousehold() {
    try {
      const { user } = await this.getCurrentUser()
      if (!user) return { user: null, household: null }

      const { data: userData, error } = await supabase
        .from('user_profiles')
        .select(`
          *,
          households (*)
        `)
        .eq('id', user.id)
        .single()

      if (error) throw error

      return { user: userData, household: (userData as { households: Record<string, unknown> }).households }
    } catch (error) {
      console.error('Error getting user with household:', error)
      return { user: null, household: null }
    }
  }
}