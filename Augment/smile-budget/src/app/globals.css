@import "tailwindcss";

/* Financial Clarity Typography System */
:root {
  --font-brand: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-ui: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  --font-data: system-ui, -apple-system, 'Segoe UI', monospace;
}

/* Font Usage Classes */
.font-brand {
  font-family: var(--font-brand);
  font-feature-settings: 'liga' 1, 'kern' 1;
}

.font-ui {
  font-family: var(--font-ui);
  font-feature-settings: 'kern' 1;
}

.font-data {
  font-family: var(--font-data);
  font-feature-settings: 'tnum' 1, 'kern' 1;
  font-variant-numeric: tabular-nums;
}

/* Financial Number Optimization */
.financial-number {
  font-family: var(--font-brand);
  font-feature-settings: 'tnum' 1; /* Tabular numbers */
  font-variant-numeric: tabular-nums;
  letter-spacing: 0.025em; /* Slight spacing for clarity */
}

.amount-display {
  font-family: var(--font-brand);
  font-weight: 600;
  font-feature-settings: 'tnum' 1, 'lnum' 1;
  letter-spacing: -0.015em; /* Tighten for large sizes */
}

.table-number {
  font-family: var(--font-ui);
  font-variant-numeric: tabular-nums;
  text-align: right; /* Right-align for better scanning */
}

/* Category Theme Applications */
.needs-theme { 
  background: rgb(248 250 252); /* needs-50 */
  color: rgb(51 65 85); /* needs-700 */
  border-color: rgb(226 232 240); /* needs-200 */
}

.wants-theme { 
  background: rgb(250 245 255); /* wants-50 */
  color: rgb(124 58 237); /* wants-700 */
  border-color: rgb(233 213 255); /* wants-200 */
}

.goals-theme { 
  background: rgb(255 247 237); /* goals-50 */
  color: rgb(194 65 12); /* goals-700 */
  border-color: rgb(254 215 170); /* goals-200 */
}

.smile-theme { 
  background: rgb(254 242 242); /* smile-50 */
  color: rgb(185 28 28); /* smile-700 */
  border-color: rgb(254 202 202); /* smile-200 */
}

/* Interactive States for Confidence */
.btn-primary {
  background: rgb(13 148 136); /* primary-600 */
  color: white;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: rgb(15 118 110); /* primary-700 */
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(13, 148, 136, 0.15);
}

/* Success Celebrations */
.celebration-success {
  background: linear-gradient(135deg, rgb(240 253 244), rgb(220 252 231)); /* success-50 to success-100 */
  border: 1px solid rgb(***********); /* success-200 */
  color: rgb(21 128 61); /* success-700 */
}

/* WCAG AA Compliance */
.text-contrast-high { 
  color: rgb(41 37 36); /* gray-800 - 7:1 ratio */
}

.text-contrast-medium { 
  color: rgb(87 83 78); /* gray-600 - 4.5:1 ratio */
}

.text-contrast-low { 
  color: rgb(***********); /* gray-500 - 3:1 ratio for large text */
}

/* Font Loading Optimization */
.font-display-swap {
  font-display: swap; /* Show fallback immediately, swap when loaded */
}

/* High Contrast for Australian Sun */
@media (prefers-contrast: high) {
  .text-primary { 
    color: rgb(28 25 23); /* gray-900 */
  }
  .text-secondary { 
    color: rgb(68 64 60); /* gray-700 */
  }
}

/* Australian Mobile Usage Optimization */
@media (max-width: 768px) {
  .dashboard-amount {
    font-size: clamp(1.25rem, 6vw, 1.875rem);
    line-height: 1.2;
  }
  
  .category-amount {
    font-size: clamp(1rem, 4vw, 1.25rem);
  }
  
  .mobile-button-text {
    font-size: 1rem; /* Minimum 16px to prevent iOS zoom */
    line-height: 1.5;
  }
  
  .mobile-input-text {
    font-size: 1rem; /* Prevent iOS zoom */
    line-height: 1.5;
  }
}

/* Reading Comfort for Families */
.mobile-body-text {
  font-size: 1rem;
  line-height: 1.6;
  letter-spacing: 0.01em;
  word-spacing: 0.05em;
}

.mobile-number-display {
  font-size: clamp(1.5rem, 5vw, 2rem);
  line-height: 1.1;
  letter-spacing: -0.02em;
  font-variant-numeric: tabular-nums;
}

/* Base body styles */
body {
  background: rgb(***********); /* gray-50 */
  color: rgb(68 64 60); /* gray-700 */
  font-family: var(--font-ui);
}