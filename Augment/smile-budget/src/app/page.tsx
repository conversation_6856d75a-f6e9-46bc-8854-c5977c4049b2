'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { AuthService } from '@/lib/database'

export default function LandingPage() {
  const [showAuthModal, setShowAuthModal] = useState(false)
  const [isSignUp, setIsSignUp] = useState(true)
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [fullName, setFullName] = useState('')
  const [authError, setAuthError] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const handleAuth = async () => {
    if (!email || !password || (isSignUp && !fullName)) {
      setAuthError('Please fill in all fields')
      return
    }

    try {
      setIsLoading(true)
      setAuthError('')

      if (isSignUp) {
        // AuthService.signUp now handles both signup and signin automatically
        const result = await AuthService.signUp(email, password, fullName)
        console.log('User signed up successfully:', result)
        
        // Store credentials temporarily in localStorage for Step 6 if session doesn't persist
        if (result.emailConfirmationRequired || !result.session) {
          console.log('Email confirmation required or no session, storing credentials temporarily')
          localStorage.setItem('tempSignupCredentials', JSON.stringify({
            email,
            password,
            fullName,
            signupComplete: true
          }))
        }
        
        // Redirect to onboarding regardless of email confirmation status
        router.push('/onboarding')
      } else {
        await AuthService.signIn(email, password)
        // After successful signin, redirect to dashboard
        router.push('/dashboard')
      }
    } catch (error) {
      console.error('Auth error:', error)
      setAuthError((error as Error).message || 'Authentication failed')
    } finally {
      setIsLoading(false)
    }
  }

  const features = [
    {
      icon: '🏠',
      title: 'NEEDS (50%)',
      description: 'Essential expenses like housing, groceries, utilities, and transport',
      color: 'needs'
    },
    {
      icon: '🎉',
      title: 'WANTS (30%)',
      description: 'Lifestyle choices like dining out, entertainment, and shopping',
      color: 'wants'
    },
    {
      icon: '🎯',
      title: 'GOALS (15%)',
      description: 'Future planning like savings, investments, and emergency funds',
      color: 'goals'
    },
    {
      icon: '😊',
      title: 'SMILE (5%)',
      description: 'Pure joy expenses like family treats and spontaneous fun',
      color: 'smile'
    }
  ]

  const benefits = [
    {
      icon: '📱',
      title: 'Easy Setup',
      description: '6-step wizard gets you started in minutes'
    },
    {
      icon: '🇦🇺',
      title: 'Made for Aussie Families',
      description: 'Designed specifically for Australian households'
    },
    {
      icon: '🔒',
      title: 'Secure & Private',
      description: 'Your financial data is encrypted and protected'
    },
    {
      icon: '📊',
      title: 'Real-time Tracking',
      description: 'Monitor your spending and progress instantly'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-success-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 px-4 py-4 sm:px-6 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-primary-600 rounded-xl flex items-center justify-center shadow-lg">
              <span className="text-white font-bold text-xl">😊</span>
            </div>
            <h1 className="font-brand font-bold text-2xl text-primary-700">Smile Budget</h1>
          </div>
          
          <div className="flex items-center gap-3">
            <button
              onClick={() => {
                setIsSignUp(false)
                setShowAuthModal(true)
              }}
              className="px-4 py-2 text-primary-700 hover:text-primary-800 font-medium transition-colors"
            >
              Sign In
            </button>
            <button
              onClick={() => {
                setIsSignUp(true)
                setShowAuthModal(true)
              }}
              className="btn-primary px-6 py-2 rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              Get Started Free
            </button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="max-w-7xl mx-auto px-4 py-16 sm:px-6 text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="font-brand font-bold text-5xl md:text-6xl text-gray-800 mb-6 leading-tight">
            Family Budgeting That Brings 
            <span className="text-primary-600"> Joy</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 leading-relaxed max-w-3xl mx-auto">
            The <strong>Smile Philosophy</strong> helps Australian families balance essential needs, 
            lifestyle wants, future goals, and most importantly - happiness and joy together.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <button
              onClick={() => {
                setIsSignUp(true)
                setShowAuthModal(true)
              }}
              className="btn-primary px-8 py-4 rounded-xl font-brand font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-200 hover:transform hover:-translate-y-1"
            >
              🚀 Start Your Free Budget
            </button>
            <button
              onClick={() => router.push('/onboarding')}
              className="bg-white border-2 border-primary-300 text-primary-700 px-8 py-4 rounded-xl font-brand font-semibold text-lg hover:bg-primary-50 transition-all duration-200"
            >
              👀 See Demo First
            </button>
          </div>

          <p className="text-sm text-gray-500">
            ✨ No credit card required • Set up in 5 minutes • Made for Australian families
          </p>
        </div>
      </section>

      {/* Smile Philosophy Section */}
      <section className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6">
          <div className="text-center mb-12">
            <h2 className="font-brand font-bold text-4xl text-gray-800 mb-4">
              The Smile Philosophy 😊
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our unique 4-category approach ensures your family&apos;s financial happiness by balancing
              essentials with joy.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => (
              <div key={index} className={`${feature.color}-theme rounded-xl p-6 text-center hover:transform hover:-translate-y-2 transition-all duration-300`}>
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className={`font-brand font-bold text-lg text-${feature.color}-800 mb-3`}>
                  {feature.title}
                </h3>
                <p className={`text-${feature.color}-700 text-sm leading-relaxed`}>
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 bg-gradient-to-r from-primary-50 to-success-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6">
          <div className="text-center mb-12">
            <h2 className="font-brand font-bold text-4xl text-gray-800 mb-4">
              Why Families Love Smile Budget
            </h2>
            <p className="text-xl text-gray-600">
              Built specifically for Australian families who want financial freedom without losing joy
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg text-2xl">
                  {benefit.icon}
                </div>
                <h3 className="font-brand font-semibold text-lg text-gray-800 mb-2">
                  {benefit.title}
                </h3>
                <p className="text-gray-600">
                  {benefit.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-primary-600 py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 text-center">
          <h2 className="font-brand font-bold text-4xl text-white mb-4">
            Ready to Transform Your Family&apos;s Finances?
          </h2>
          <p className="text-xl text-primary-100 mb-8">
            Join thousands of Australian families already using the Smile Philosophy
          </p>
          <button
            onClick={() => {
              setIsSignUp(true)
              setShowAuthModal(true)
            }}
            className="bg-white text-primary-600 px-8 py-4 rounded-xl font-brand font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-200 hover:transform hover:-translate-y-1"
          >
            🎉 Start Your Free Budget Today
          </button>
          <p className="text-primary-200 text-sm mt-4">
            No credit card required • Free forever • Set up in 5 minutes
          </p>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 text-center">
          <div className="flex items-center justify-center gap-2 mb-4">
            <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold">😊</span>
            </div>
            <span className="font-brand font-semibold text-white">Smile Budget</span>
          </div>
          <p className="text-gray-400 text-sm">
            Made with ❤️ for Australian families. Built with Next.js and Supabase.
          </p>
        </div>
      </footer>

      {/* Authentication Modal */}
      {showAuthModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl p-8 max-w-md w-full mx-4 shadow-2xl">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white font-bold text-2xl">😊</span>
              </div>
              <h3 className="font-brand font-bold text-2xl text-gray-800">
                {isSignUp ? 'Create Your Account' : 'Welcome Back'}
              </h3>
              <p className="text-gray-600 mt-2">
                {isSignUp 
                  ? 'Start your journey to financial happiness'
                  : 'Sign in to continue to your dashboard'
                }
              </p>
            </div>
            
            <div className="space-y-4">
              {isSignUp && (
                <div>
                  <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-1">
                    Full Name
                  </label>
                  <input
                    type="text"
                    id="fullName"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Your full name"
                  />
                </div>
              )}
              
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="<EMAIL>"
                />
              </div>
              
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  Password
                </label>
                <input
                  type="password"
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Enter your password"
                />
              </div>
              
              {authError && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-700 text-sm">{authError}</p>
                </div>
              )}
              
              <button
                onClick={handleAuth}
                disabled={isLoading}
                className={`w-full btn-primary py-3 rounded-lg font-brand font-semibold transition-all duration-200 ${
                  isLoading ? 'opacity-50 cursor-not-allowed' : 'hover:transform hover:-translate-y-0.5'
                }`}
              >
                {isLoading ? (
                  <>
                    <span className="animate-spin inline-block mr-2">⏳</span>
                    {isSignUp ? 'Creating Account...' : 'Signing In...'}
                  </>
                ) : (
                  <>{isSignUp ? '🚀 Create Account & Start Budget' : '🎉 Sign In to Dashboard'}</>
                )}
              </button>
              
              <div className="text-center space-y-3">
                <button
                  type="button"
                  onClick={() => {
                    setIsSignUp(!isSignUp)
                    setAuthError('')
                    setEmail('')
                    setPassword('')
                    setFullName('')
                  }}
                  className="text-primary-600 hover:text-primary-700 text-sm font-medium"
                >
                  {isSignUp ? 'Already have an account? Sign in' : 'Need an account? Sign up'}
                </button>
                
                <button
                  type="button"
                  onClick={() => {
                    setShowAuthModal(false)
                    setAuthError('')
                    setEmail('')
                    setPassword('')
                    setFullName('')
                  }}
                  className="block mx-auto text-gray-500 hover:text-gray-700 text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}