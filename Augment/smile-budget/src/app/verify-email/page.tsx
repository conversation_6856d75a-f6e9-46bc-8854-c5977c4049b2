'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { AuthService } from '@/lib/database'

export default function VerifyEmailPage() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error' | 'expired'>('loading')
  const [message, setMessage] = useState('')
  const router = useRouter()
  const searchParams = useSearchParams()

  useEffect(() => {
    const handleEmailVerification = async () => {
      try {
        // Get verification parameters from URL
        const token = searchParams.get('token')
        const type = searchParams.get('type')
        
        console.log('Email verification attempt:', { token: token?.substring(0, 10) + '...', type })

        if (!token || type !== 'email') {
          setStatus('error')
          setMessage('Invalid verification link. Please check your email for the correct link.')
          return
        }

        // Process the verification
        const result = await AuthService.verifyEmail(token)
        
        if (result.success) {
          setStatus('success')
          setMessage('Email verified successfully! You now have full access to all features.')
          
          // Clear any pending user data since they're now verified
          AuthService.clearPendingUser()
          
          // Redirect to dashboard after a short delay
          setTimeout(() => {
            router.push('/dashboard')
          }, 3000)
        } else {
          setStatus('error')
          setMessage(result.error || 'Verification failed. Please try again or contact support.')
        }
      } catch (error) {
        console.error('Email verification error:', error)
        setStatus('error')
        setMessage('An unexpected error occurred. Please try again or contact support.')
      }
    }

    handleEmailVerification()
  }, [searchParams, router])

  const handleResendVerification = async () => {
    try {
      await AuthService.resendVerificationEmail()
      setMessage('New verification email sent! Please check your inbox.')
    } catch (error) {
      console.error('Error resending verification:', error)
      setMessage('Failed to send verification email. Please try again.')
    }
  }

  const getStatusIcon = () => {
    switch (status) {
      case 'loading':
        return '⏳'
      case 'success':
        return '✅'
      case 'error':
      case 'expired':
        return '❌'
      default:
        return '📧'
    }
  }

  const getStatusColor = () => {
    switch (status) {
      case 'loading':
        return 'text-blue-600'
      case 'success':
        return 'text-green-600'
      case 'error':
      case 'expired':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">😊</span>
            </div>
            <h1 className="font-brand font-bold text-2xl text-primary-700">Smile Budget</h1>
          </div>
        </div>

        {/* Verification Status Card */}
        <div className="bg-white rounded-xl shadow-soft p-8 text-center">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-6 mx-auto">
            <span className={`text-3xl ${status === 'loading' ? 'animate-spin' : ''}`}>
              {getStatusIcon()}
            </span>
          </div>

          <h2 className={`font-brand font-bold text-2xl mb-4 ${getStatusColor()}`}>
            {status === 'loading' && 'Verifying Your Email...'}
            {status === 'success' && 'Email Verified!'}
            {status === 'error' && 'Verification Failed'}
            {status === 'expired' && 'Link Expired'}
          </h2>

          <p className="text-gray-600 mb-6 leading-relaxed">
            {message}
          </p>

          {/* Action Buttons */}
          <div className="space-y-3">
            {status === 'success' && (
              <button
                onClick={() => router.push('/dashboard')}
                className="w-full btn-primary py-3 rounded-lg font-brand font-semibold"
              >
                Go to Dashboard
              </button>
            )}

            {(status === 'error' || status === 'expired') && (
              <>
                <button
                  onClick={handleResendVerification}
                  className="w-full btn-primary py-3 rounded-lg font-brand font-semibold"
                >
                  Send New Verification Email
                </button>
                <button
                  onClick={() => router.push('/dashboard')}
                  className="w-full px-6 py-3 border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Continue to Dashboard
                </button>
              </>
            )}

            {status === 'loading' && (
              <div className="text-sm text-gray-500">
                This may take a few moments...
              </div>
            )}
          </div>
        </div>

        {/* Help Text */}
        <div className="text-center mt-6">
          <p className="text-sm text-gray-500">
            Having trouble? Check your spam folder or{' '}
            <button
              onClick={handleResendVerification}
              className="text-primary-600 hover:text-primary-700 underline"
            >
              request a new verification email
            </button>
          </p>
        </div>
      </div>
    </div>
  )
}
