'use client'

import { useState } from 'react'
import OnboardingLayout from '@/components/onboarding/OnboardingLayout'
import Step1<PERSON>bo<PERSON>Y<PERSON> from '@/components/onboarding/Step1AboutYou'
import Step2IncomeInput from '@/components/onboarding/Step2IncomeInput'
import Step3BudgetAllocation from '@/components/onboarding/Step3BudgetAllocation'
import Step4CategorySelection from '@/components/onboarding/Step4CategorySelection'
import Step5BudgetAmounts from '@/components/onboarding/Step5BudgetAmounts'
import Step6ReviewLaunch from '@/components/onboarding/Step6ReviewLaunch'

// Onboarding data interface
export interface OnboardingData {
  // Step 1: About You
  householdName: string
  yourName: string
  householdComposition: {
    adults: number
    children: number
    pets: boolean
  }
  
  // Step 2: Income Input
  incomeSourcess: {
    id: string
    name: string
    type: 'salary' | 'freelance' | 'investment' | 'rental' | 'business' | 'pension' | 'other'
    frequency: 'weekly' | 'fortnightly' | 'monthly' | 'quarterly' | 'annually'
    amount: number
  }[]
  totalMonthlyIncome: number
  
  // Step 3: Budget Allocation
  allocations: {
    needs: number    // percentage
    wants: number    // percentage
    goals: number    // percentage
    smile: number    // percentage
  }
  
  // Step 4: Category Selection
  selectedCategories: {
    needs: string[]
    wants: string[]
    goals: string[]
    smile: string[]
  }
  
  // Step 5: Budget Amounts
  categoryAmounts?: {
    categoryId: string
    amount: number
  }[]
  
  // Step 6: Review
  isComplete: boolean
}

export default function OnboardingPage() {
  const [currentStep, setCurrentStep] = useState(1)
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    householdName: '',
    yourName: '',
    householdComposition: {
      adults: 2,
      children: 0,
      pets: false
    },
    incomeSourcess: [],
    totalMonthlyIncome: 0,
    allocations: {
      needs: 50,
      wants: 20,
      goals: 25,
      smile: 5
    },
    selectedCategories: {
      needs: [],
      wants: [],
      goals: [],
      smile: []
    },
    categoryAmounts: [],
    isComplete: false
  })

  const steps = [
    { number: 1, title: 'About You', description: 'Tell us about your household' },
    { number: 2, title: 'Income', description: 'Add your income sources' },
    { number: 3, title: 'Allocation', description: 'Set your budget balance' },
    { number: 4, title: 'Categories', description: 'Choose your spending areas' },
    { number: 5, title: 'Amounts', description: 'Set category budgets' },
    { number: 6, title: 'Launch', description: 'Review and start budgeting' }
  ]

  const updateOnboardingData = (updates: Partial<OnboardingData>) => {
    setOnboardingData(prev => ({ ...prev, ...updates }))
  }

  const nextStep = () => {
    if (currentStep < 6) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const goToStep = (step: number) => {
    setCurrentStep(step)
  }

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <Step1AboutYou
            data={onboardingData}
            updateData={updateOnboardingData}
            onNext={nextStep}
          />
        )
      case 2:
        return (
          <Step2IncomeInput
            data={onboardingData}
            updateData={updateOnboardingData}
            onNext={nextStep}
            onPrev={prevStep}
          />
        )
      case 3:
        return (
          <Step3BudgetAllocation
            data={onboardingData}
            updateData={updateOnboardingData}
            onNext={nextStep}
            onPrev={prevStep}
          />
        )
      case 4:
        return (
          <Step4CategorySelection
            data={onboardingData}
            updateData={updateOnboardingData}
            onNext={nextStep}
            onPrev={prevStep}
          />
        )
      case 5:
        return (
          <Step5BudgetAmounts
            data={onboardingData}
            updateData={updateOnboardingData}
            onNext={nextStep}
            onPrev={prevStep}
          />
        )
      case 6:
        return (
          <Step6ReviewLaunch
            data={onboardingData}
            updateData={updateOnboardingData}
            onPrev={prevStep}
            goToStep={goToStep}
          />
        )
      default:
        return null
    }
  }

  return (
    <OnboardingLayout
      currentStep={currentStep}
      steps={steps}
      onStepClick={goToStep}
    >
      {renderCurrentStep()}
    </OnboardingLayout>
  )
}