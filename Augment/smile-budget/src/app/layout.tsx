import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import SupabaseProvider from "@/components/providers/supabase-provider";
import ClientOnly from "@/components/client-only";

const inter = Inter({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'],
  display: 'swap',
  variable: '--font-inter',
});

export const metadata: Metadata = {
  title: "Smile Budget - Family Budgeting Made Joyful",
  description: "Transform family budgeting from a restrictive chore into an exciting journey toward shared dreams and financial goals.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${inter.variable} font-sans antialiased`}
      >
        <ClientOnly>
          <SupabaseProvider>
            {children}
          </SupabaseProvider>
        </ClientOnly>
      </body>
    </html>
  );
}
