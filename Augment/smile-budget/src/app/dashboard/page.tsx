'use client'

import { useEffect, useState, useCallback } from 'react'
import { AuthService, DatabaseService } from '@/lib/database'
import { useRouter } from 'next/navigation'

interface BudgetCategory {
  id: string
  name: string
  category_type: string
  allocated_amount: number
  actual_amount: number
}

interface BudgetSummary {
  needs: { planned: number; actual: number; categories: BudgetCategory[] }
  wants: { planned: number; actual: number; categories: BudgetCategory[] }
  goals: { planned: number; actual: number; categories: BudgetCategory[] }
  smile: { planned: number; actual: number; categories: BudgetCategory[] }
}


interface Household {
  id: string
  name: string
  currency_code: string
  timezone: string
}

export default function DashboardPage() {
  const [household, setHousehold] = useState<Household | null>(null)
  const [budgetSummary, setBudgetSummary] = useState<BudgetSummary | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const router = useRouter()

  const checkAuthAndLoadData = useCallback(async () => {
    try {
      // Check authentication
      const { user: currentUser } = await AuthService.getCurrentUser()
      if (!currentUser) {
        router.push('/onboarding')
        return
      }

      // Get user with household data
      const { user: userData, household: householdData } = await AuthService.getUserWithHousehold()
      if (!userData || !householdData) {
        router.push('/onboarding')
        return
      }

      setHousehold(householdData as unknown as Household)

      // Load budget data
      const budgetData = await DatabaseService.getBudgetSummary((householdData as unknown as Household).id)
      setBudgetSummary(budgetData.summary)

    } catch (error) {
      console.error('Error loading dashboard data:', error)
      setError('Failed to load dashboard data')
    } finally {
      setLoading(false)
    }
  }, [router])

  useEffect(() => {
    checkAuthAndLoadData()
  }, [checkAuthAndLoadData])

  const handleSignOut = async () => {
    try {
      await AuthService.signOut()
      router.push('/')
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mb-4 mx-auto">
            <span className="text-2xl animate-spin">⏳</span>
          </div>
          <h2 className="font-brand font-semibold text-xl text-gray-800">Loading your Smile Budget...</h2>
          <p className="text-gray-600">Please wait while we prepare your dashboard</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4 mx-auto">
            <span className="text-2xl">⚠️</span>
          </div>
          <h2 className="font-brand font-semibold text-xl text-gray-800 mb-2">Oops! Something went wrong</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button 
            onClick={() => router.push('/onboarding')}
            className="btn-primary px-6 py-2 rounded-lg font-medium"
          >
            Return to Onboarding
          </button>
        </div>
      </div>
    )
  }

  const categoryTypes = [
    { key: 'needs' as const, name: 'NEEDS', icon: '🏠', color: 'needs' },
    { key: 'wants' as const, name: 'WANTS', icon: '🎉', color: 'wants' },
    { key: 'goals' as const, name: 'GOALS', icon: '🎯', color: 'goals' },
    { key: 'smile' as const, name: 'SMILE', icon: '😊', color: 'smile' }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-4 py-4 sm:px-6">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">😊</span>
            </div>
            <h1 className="font-brand font-bold text-2xl text-primary-700">Smile Budget</h1>
          </div>
          
          <div className="flex items-center gap-4">
            <div className="text-right">
              <p className="font-brand font-semibold text-sm text-gray-800">{household?.name}</p>
              <p className="text-xs text-gray-500">Welcome back!</p>
            </div>
            <button
              onClick={handleSignOut}
              className="px-4 py-2 text-sm border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
            >
              Sign Out
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 py-6 sm:px-6">
        
        {/* Welcome Banner */}
        <div className="mb-8 p-6 bg-gradient-to-r from-success-50 to-primary-50 border border-success-200 rounded-xl">
          <div className="flex items-center gap-3">
            <span className="text-3xl">🎉</span>
            <div>
              <h2 className="font-brand font-bold text-2xl text-success-700">
                Welcome to your Smile Budget Dashboard!
              </h2>
              <p className="text-success-600">
                Your family budget has been created successfully. Start tracking your expenses and building toward your goals!
              </p>
            </div>
          </div>
        </div>

        {/* Budget Overview */}
        {budgetSummary && (
          <section className="mb-8 bg-white rounded-xl shadow-soft p-6">
            <h3 className="font-brand font-semibold text-xl text-gray-800 mb-6">Budget Overview</h3>
            
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
              {categoryTypes.map(type => {
                const data = budgetSummary[type.key]
                const progressPercentage = data.planned > 0 ? (data.actual / data.planned) * 100 : 0
                
                return (
                  <div key={type.key} className={`${type.color}-theme rounded-lg p-4`}>
                    <div className="flex items-center gap-2 mb-3">
                      <span className={`text-${type.color}-600 text-xl`}>{type.icon}</span>
                      <span className={`font-brand font-semibold text-sm text-${type.color}-700`}>{type.name}</span>
                    </div>
                    
                    <div className="mb-3">
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-xs text-gray-600">Spent</span>
                        <span className="text-xs text-gray-600">
                          ${data.actual.toLocaleString()} / ${data.planned.toLocaleString()}
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full transition-all duration-500 ${
                            progressPercentage > 100 ? 'bg-red-500' : `bg-${type.color}-500`
                          }`}
                          style={{ width: `${Math.min(progressPercentage, 100)}%` }}
                        ></div>
                      </div>
                    </div>
                    
                    <div className="text-center">
                      <p className={`font-data font-bold text-lg text-${type.color}-800`}>
                        ${(data.planned - data.actual).toLocaleString()}
                      </p>
                      <p className={`text-xs text-${type.color}-600`}>remaining</p>
                    </div>
                  </div>
                )
              })}
            </div>
          </section>
        )}

        {/* Quick Actions */}
        <section className="mb-8 bg-white rounded-xl shadow-soft p-6">
          <h3 className="font-brand font-semibold text-xl text-gray-800 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors">
              <div className="text-center">
                <span className="text-3xl mb-2 block">💳</span>
                <p className="font-brand font-semibold text-gray-800">Add Expense</p>
                <p className="text-sm text-gray-600">Record a new expense</p>
              </div>
            </button>
            
            <button className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors">
              <div className="text-center">
                <span className="text-3xl mb-2 block">📊</span>
                <p className="font-brand font-semibold text-gray-800">View Reports</p>
                <p className="text-sm text-gray-600">See spending insights</p>
              </div>
            </button>
            
            <button className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors">
              <div className="text-center">
                <span className="text-3xl mb-2 block">⚙️</span>
                <p className="font-brand font-semibold text-gray-800">Settings</p>
                <p className="text-sm text-gray-600">Adjust your budget</p>
              </div>
            </button>
          </div>
        </section>

        {/* Coming Soon */}
        <section className="bg-white rounded-xl shadow-soft p-6">
          <h3 className="font-brand font-semibold text-xl text-gray-800 mb-4">Coming Soon</h3>
          <div className="space-y-3">
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <span className="text-xl">📱</span>
              <div>
                <p className="font-medium text-gray-800">Expense Tracking</p>
                <p className="text-sm text-gray-600">Add and categorize your daily expenses</p>
              </div>
            </div>
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <span className="text-xl">🎯</span>
              <div>
                <p className="font-medium text-gray-800">Goal Tracking</p>
                <p className="text-sm text-gray-600">Monitor progress toward your savings goals</p>
              </div>
            </div>
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <span className="text-xl">📈</span>
              <div>
                <p className="font-medium text-gray-800">Spending Insights</p>
                <p className="text-sm text-gray-600">Detailed reports and trends</p>
              </div>
            </div>
          </div>
        </section>

      </main>
    </div>
  )
}