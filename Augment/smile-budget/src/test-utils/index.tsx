import React from 'react'

// Mock data for testing
export const mockUser = {
  id: '123',
  email: '<EMAIL>',
  email_confirmed_at: '2024-01-01T00:00:00Z',
  user_metadata: {},
  app_metadata: {},
  aud: 'authenticated',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
}

export const mockPendingUser = {
  id: '456',
  email: '<EMAIL>',
  name: 'Pending User',
  householdId: 'household-123',
}

export const mockHousehold = {
  id: 'household-123',
  name: 'Test Household',
  monthly_income: 5000,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
}

export const mockBudgetSummary = {
  needs: { allocated: 2500, spent: 1200, remaining: 1300 },
  wants: { allocated: 1500, spent: 800, remaining: 700 },
  goals: { allocated: 750, spent: 200, remaining: 550 },
  smile: { allocated: 250, spent: 50, remaining: 200 },
}

// Supabase mock responses
export const mockSupabaseResponses = {
  signUpSuccess: {
    data: { user: mockUser, session: null },
    error: null,
  },
  signUpError: {
    data: { user: null, session: null },
    error: { message: 'Email already registered' },
  },
  signInSuccess: {
    data: { user: mockUser, session: { access_token: 'token' } },
    error: null,
  },
  signInError: {
    data: { user: null, session: null },
    error: { message: 'Invalid credentials' },
  },
  verifyOtpSuccess: {
    data: { user: mockUser, session: { access_token: 'token' } },
    error: null,
  },
  verifyOtpError: {
    data: { user: null, session: null },
    error: { message: 'Invalid token' },
  },
}

// Custom render function will be defined in individual test files to avoid hook issues

// Helper functions for testing
export const mockLocalStorage = () => {
  const store: { [key: string]: string } = {}
  
  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key]
    }),
    clear: jest.fn(() => {
      Object.keys(store).forEach(key => delete store[key])
    }),
  }
}

export const mockSupabaseMethod = (method: any, response: any) => {
  method.mockResolvedValue(response)
}
