import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'

// Mock data for testing
export const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  email_confirmed_at: '2024-01-01T00:00:00Z',
  user_metadata: {
    full_name: 'Test User',
  },
}

export const mockPendingUser = {
  userId: 'test-user-id',
  email: '<EMAIL>',
  fullName: 'Test User',
  needsVerification: true,
  createdAt: '2024-01-01T00:00:00Z',
}

export const mockHousehold = {
  id: 'test-household-id',
  name: 'Test Household',
  monthly_income: 5000,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
}

export const mockBudgetSummary = {
  totalIncome: 5000,
  totalExpenses: 3500,
  totalSavings: 1500,
  categories: [
    {
      id: 'needs',
      name: 'Needs',
      category_type: 'needs',
      allocated_amount: 2500,
      spent_amount: 2000,
      percentage: 50,
    },
    {
      id: 'wants',
      name: 'Wants',
      category_type: 'wants',
      allocated_amount: 1500,
      spent_amount: 1000,
      percentage: 30,
    },
    {
      id: 'goals',
      name: 'Goals',
      category_type: 'goals',
      allocated_amount: 750,
      spent_amount: 500,
      percentage: 15,
    },
    {
      id: 'smile',
      name: 'Smile',
      category_type: 'smile',
      allocated_amount: 250,
      spent_amount: 0,
      percentage: 5,
    },
  ],
}

// Supabase mock responses
export const mockSupabaseResponses = {
  signUpSuccess: {
    data: {
      user: mockUser,
      session: null,
    },
    error: null,
  },
  signUpError: {
    data: { user: null, session: null },
    error: { message: 'Email already registered' },
  },
  signInSuccess: {
    data: {
      user: mockUser,
      session: {
        access_token: 'mock-token',
        refresh_token: 'mock-refresh-token',
      },
    },
    error: null,
  },
  signInError: {
    data: { user: null, session: null },
    error: { message: 'Invalid credentials' },
  },
  getUserSuccess: {
    data: { user: mockUser },
    error: null,
  },
  getUserNull: {
    data: { user: null },
    error: null,
  },
  verifyOtpSuccess: {
    data: {
      user: mockUser,
      session: {
        access_token: 'mock-token',
        refresh_token: 'mock-refresh-token',
      },
    },
    error: null,
  },
  verifyOtpError: {
    data: { user: null, session: null },
    error: { message: 'Invalid or expired token' },
  },
  databaseSuccess: {
    data: mockHousehold,
    error: null,
  },
  databaseError: {
    data: null,
    error: { message: 'Database error' },
  },
}

// Custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return <>{children}</>
}

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

export * from '@testing-library/react'
export { customRender as render }

// Helper functions for testing
export const waitForLoadingToFinish = () => {
  return new Promise((resolve) => setTimeout(resolve, 0))
}

export const mockLocalStorage = (items: Record<string, string> = {}) => {
  const localStorageMock = {
    getItem: jest.fn((key: string) => items[key] || null),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  }
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
    writable: true,
  })
  return localStorageMock
}

export const mockSupabaseMethod = (method: string, response: any) => {
  const { supabase } = require('@/lib/supabase')
  if (method.includes('.')) {
    const [parent, child] = method.split('.')
    supabase[parent][child].mockResolvedValue(response)
  } else {
    supabase[method].mockResolvedValue(response)
  }
}
