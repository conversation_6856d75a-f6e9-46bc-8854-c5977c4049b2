import { AuthService } from '@/lib/database'
import { 
  mockUser, 
  mockPendingUser, 
  mockSupabaseResponses,
  mockLocalStorage,
  mockSupabaseMethod 
} from '@/test-utils'

// Mock the supabase module
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      signUp: jest.fn(),
      signInWithPassword: jest.fn(),
      signOut: jest.fn(),
      getSession: jest.fn(),
      resend: jest.fn(),
      verifyOtp: jest.fn(),
    },
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(),
        })),
      })),
    })),
  },
}))

// Import the mocked supabase after mocking
import { supabase } from '@/lib/supabase'

describe('AuthService', () => {
  let mockLS: ReturnType<typeof mockLocalStorage>

  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks()
    
    // Mock localStorage
    mockLS = mockLocalStorage()
    Object.defineProperty(global, 'localStorage', {
      value: mockLS,
      writable: true,
    })
  })

  describe('signUp', () => {
    it('should create a new user account successfully', async () => {
      // Arrange
      const email = '<EMAIL>'
      const password = 'password123'
      const fullName = 'Test User'
      
      mockSupabaseMethod(supabase.auth.signUp, mockSupabaseResponses.signUpSuccess)

      // Act
      const result = await AuthService.signUp(email, password, fullName)

      // Assert
      expect(supabase.auth.signUp).toHaveBeenCalledWith({
        email,
        password,
        options: {
          data: {
            full_name: fullName
          }
        }
      })
      
      expect(result.success).toBe(true)
      expect(result.user).toEqual(mockUser)
      expect(result.needsVerification).toBe(true)
      expect(result.pendingUser).toBeDefined()
      
      // Check that pending user data was stored in localStorage
      expect(mockLS.setItem).toHaveBeenCalledWith(
        'pendingUser',
        expect.stringContaining(email)
      )
    })

    it('should handle signup errors', async () => {
      // Arrange
      const email = '<EMAIL>'
      const password = 'password123'
      const fullName = 'Test User'

      // Mock to throw an error directly
      supabase.auth.signUp = jest.fn().mockRejectedValue(new Error('Email already registered'))

      // Act & Assert
      await expect(AuthService.signUp(email, password, fullName))
        .rejects.toThrow('Email already registered')
    })

    it('should handle missing user data in response', async () => {
      // Arrange
      const email = '<EMAIL>'
      const password = 'password123'
      const fullName = 'Test User'
      
      const responseWithoutUser = {
        data: { user: null, session: null },
        error: null,
      }
      
      mockSupabaseMethod(supabase.auth.signUp, responseWithoutUser)

      // Act & Assert
      await expect(AuthService.signUp(email, password, fullName))
        .rejects.toThrow('User creation failed - no user data returned')
    })
  })

  describe('signIn', () => {
    it('should sign in user successfully', async () => {
      // Arrange
      const email = '<EMAIL>'
      const password = 'password123'
      
      mockSupabaseMethod(supabase.auth.signInWithPassword, mockSupabaseResponses.signInSuccess)

      // Act
      const result = await AuthService.signIn(email, password)

      // Assert
      expect(supabase.auth.signInWithPassword).toHaveBeenCalledWith({
        email,
        password
      })
      
      expect(result.success).toBe(true)
      expect(result.user).toEqual(mockUser)
      expect(result.session).toBeDefined()
    })

    it('should handle signin errors', async () => {
      // Arrange
      const email = '<EMAIL>'
      const password = 'wrongpassword'

      // Mock to throw an error directly
      supabase.auth.signInWithPassword = jest.fn().mockRejectedValue(new Error('Invalid credentials'))

      // Act & Assert
      await expect(AuthService.signIn(email, password))
        .rejects.toThrow('Invalid credentials')
    })
  })

  describe('signOut', () => {
    it('should sign out user successfully', async () => {
      // Arrange
      mockSupabaseMethod(supabase.auth.signOut, { error: null })

      // Act
      const result = await AuthService.signOut()

      // Assert
      expect(supabase.auth.signOut).toHaveBeenCalled()
      expect(result.success).toBe(true)
    })

    it('should handle signout errors', async () => {
      // Arrange
      // Mock to throw an error directly
      supabase.auth.signOut = jest.fn().mockRejectedValue(new Error('Signout failed'))

      // Act & Assert
      await expect(AuthService.signOut())
        .rejects.toThrow('Signout failed')
    })
  })

  describe('getCurrentUser', () => {
    it('should return current user session', async () => {
      // Arrange
      const sessionResponse = {
        data: { session: { user: mockUser, access_token: 'token' } },
        error: null
      }
      mockSupabaseMethod(supabase.auth.getSession, sessionResponse)

      // Act
      const result = await AuthService.getCurrentUser()

      // Assert
      expect(supabase.auth.getSession).toHaveBeenCalled()
      expect(result.user).toEqual(mockUser)
      expect(result.session).toBeDefined()
    })

    it('should handle no session', async () => {
      // Arrange
      const noSessionResponse = {
        data: { session: null },
        error: null
      }
      mockSupabaseMethod(supabase.auth.getSession, noSessionResponse)

      // Act
      const result = await AuthService.getCurrentUser()

      // Assert
      expect(result.user).toBeNull()
      expect(result.session).toBeNull()
    })

    it('should handle session errors gracefully', async () => {
      // Arrange
      const sessionError = {
        data: { session: null },
        error: { message: 'Session error' }
      }
      mockSupabaseMethod(supabase.auth.getSession, sessionError)

      // Act
      const result = await AuthService.getCurrentUser()

      // Assert
      expect(result.user).toBeNull()
      expect(result.session).toBeNull()
    })
  })

  describe('getPendingUser', () => {
    it('should return pending user data from localStorage', () => {
      // Arrange
      mockLS.getItem.mockReturnValue(JSON.stringify(mockPendingUser))

      // Act
      const result = AuthService.getPendingUser()

      // Assert
      expect(mockLS.getItem).toHaveBeenCalledWith('pendingUser')
      expect(result).toEqual(mockPendingUser)
    })

    it('should return null when no pending user data', () => {
      // Arrange
      mockLS.getItem.mockReturnValue(null)

      // Act
      const result = AuthService.getPendingUser()

      // Assert
      expect(result).toBeNull()
    })

    it('should handle invalid JSON gracefully', () => {
      // Arrange
      mockLS.getItem.mockReturnValue('invalid-json')

      // Act
      const result = AuthService.getPendingUser()

      // Assert
      expect(result).toBeNull()
    })
  })

  describe('clearPendingUser', () => {
    it('should remove pending user data from localStorage', () => {
      // Act
      AuthService.clearPendingUser()

      // Assert
      expect(mockLS.removeItem).toHaveBeenCalledWith('pendingUser')
    })

    it('should handle localStorage errors gracefully', () => {
      // Arrange
      mockLS.removeItem.mockImplementation(() => {
        throw new Error('localStorage error')
      })

      // Act & Assert - should not throw
      expect(() => AuthService.clearPendingUser()).not.toThrow()
    })
  })

  describe('checkVerificationStatus', () => {
    it('should return verification status for authenticated user', async () => {
      // Arrange
      const sessionResponse = {
        data: { session: { user: { ...mockUser, email_confirmed_at: null } } },
        error: null
      }
      mockSupabaseMethod(supabase.auth.getSession, sessionResponse)

      // Act
      const result = await AuthService.checkVerificationStatus()

      // Assert
      expect(result.needsVerification).toBe(true)
      expect(result.isPending).toBe(false)
      expect(result.user).toBeDefined()
    })

    it('should return verification status for verified user', async () => {
      // Arrange
      const verifiedUser = { ...mockUser, email_confirmed_at: '2023-01-01T00:00:00Z' }
      const sessionResponse = {
        data: { session: { user: verifiedUser } },
        error: null
      }
      mockSupabaseMethod(supabase.auth.getSession, sessionResponse)

      // Act
      const result = await AuthService.checkVerificationStatus()

      // Assert
      expect(result.needsVerification).toBe(false)
      expect(result.isPending).toBe(false)
      expect(result.user).toEqual(verifiedUser)
    })

    it('should return pending status when no user but pending data exists', async () => {
      // Arrange
      const noSessionResponse = {
        data: { session: null },
        error: null
      }
      mockSupabaseMethod(supabase.auth.getSession, noSessionResponse)
      mockLS.getItem.mockReturnValue(JSON.stringify(mockPendingUser))

      // Act
      const result = await AuthService.checkVerificationStatus()

      // Assert
      expect(result.needsVerification).toBe(true)
      expect(result.isPending).toBe(true)
      expect(result.user).toBeNull()
    })

    it('should handle errors gracefully', async () => {
      // Arrange
      const sessionError = {
        data: { session: null },
        error: { message: 'Session error' }
      }
      mockSupabaseMethod(supabase.auth.getSession, sessionError)

      // Act
      const result = await AuthService.checkVerificationStatus()

      // Assert
      expect(result.needsVerification).toBe(false)
      expect(result.isPending).toBe(false)
      expect(result.user).toBeNull()
    })
  })

  describe('resendVerificationEmail', () => {
    it('should resend verification email with provided email', async () => {
      // Arrange
      const email = '<EMAIL>'
      mockSupabaseMethod(supabase.auth.resend, { error: null })

      // Act
      const result = await AuthService.resendVerificationEmail(email)

      // Assert
      expect(supabase.auth.resend).toHaveBeenCalledWith({
        type: 'signup',
        email
      })
      expect(result.success).toBe(true)
      expect(result.email).toBe(email)
    })

    it('should use pending user email when no email provided', async () => {
      // Arrange
      mockLS.getItem.mockReturnValue(JSON.stringify(mockPendingUser))
      mockSupabaseMethod(supabase.auth.resend, { error: null })

      // Act
      const result = await AuthService.resendVerificationEmail()

      // Assert
      expect(supabase.auth.resend).toHaveBeenCalledWith({
        type: 'signup',
        email: mockPendingUser.email
      })
      expect(result.success).toBe(true)
      expect(result.email).toBe(mockPendingUser.email)
    })

    it('should throw error when no email available', async () => {
      // Arrange
      mockLS.getItem.mockReturnValue(null)

      // Act & Assert
      await expect(AuthService.resendVerificationEmail())
        .rejects.toThrow('No email address available for verification')
    })

    it('should handle resend errors', async () => {
      // Arrange
      const email = '<EMAIL>'
      // Mock to throw an error directly
      supabase.auth.resend = jest.fn().mockRejectedValue(new Error('Resend failed'))

      // Act & Assert
      await expect(AuthService.resendVerificationEmail(email))
        .rejects.toThrow('Resend failed')
    })
  })

  describe('verifyEmail', () => {
    it('should verify email successfully', async () => {
      // Arrange
      const token = 'verification-token'
      mockSupabaseMethod(supabase.auth.verifyOtp, mockSupabaseResponses.verifyOtpSuccess)

      // Act
      const result = await AuthService.verifyEmail(token)

      // Assert
      expect(supabase.auth.verifyOtp).toHaveBeenCalledWith({
        token_hash: token,
        type: 'email'
      })
      expect(result.success).toBe(true)
      expect(result.user).toEqual(mockUser)
      expect(mockLS.removeItem).toHaveBeenCalledWith('pendingUser')
    })

    it('should handle verification errors', async () => {
      // Arrange
      const token = 'invalid-token'
      mockSupabaseMethod(supabase.auth.verifyOtp, mockSupabaseResponses.verifyOtpError)

      // Act
      const result = await AuthService.verifyEmail(token)

      // Assert
      expect(result.success).toBe(false)
      expect(result.error).toBe('Invalid token')
    })

    it('should handle missing user data in verification response', async () => {
      // Arrange
      const token = 'verification-token'
      const responseWithoutUser = {
        data: { user: null, session: null },
        error: null,
      }
      mockSupabaseMethod(supabase.auth.verifyOtp, responseWithoutUser)

      // Act
      const result = await AuthService.verifyEmail(token)

      // Assert
      expect(result.success).toBe(false)
      expect(result.error).toBe('Verification failed - no user data returned')
    })

    it('should handle unexpected errors', async () => {
      // Arrange
      const token = 'verification-token'
      supabase.auth.verifyOtp = jest.fn().mockRejectedValue(new Error('Network error'))

      // Act
      const result = await AuthService.verifyEmail(token)

      // Assert
      expect(result.success).toBe(false)
      expect(result.error).toBe('An unexpected error occurred during verification')
    })
  })

  describe('completeEmailVerification', () => {
    it('should complete email verification successfully', async () => {
      // Arrange
      const sessionResponse = {
        data: { session: { user: mockUser, access_token: 'token' } },
        error: null
      }
      mockSupabaseMethod(supabase.auth.getSession, sessionResponse)

      // Act
      const result = await AuthService.completeEmailVerification()

      // Assert
      expect(result.success).toBe(true)
      expect(result.user).toEqual(mockUser)
      expect(result.session).toBeDefined()
      expect(mockLS.removeItem).toHaveBeenCalledWith('pendingUser')
    })

    it('should handle no session after verification', async () => {
      // Arrange
      const noSessionResponse = {
        data: { session: null },
        error: null
      }
      mockSupabaseMethod(supabase.auth.getSession, noSessionResponse)

      // Act
      const result = await AuthService.completeEmailVerification()

      // Assert
      expect(result.success).toBe(false)
      expect(result.user).toBeNull()
      expect(result.session).toBeNull()
    })

    it('should handle session errors', async () => {
      // Arrange
      // Mock to throw an error directly
      supabase.auth.getSession = jest.fn().mockRejectedValue(new Error('Session error'))

      // Act & Assert
      await expect(AuthService.completeEmailVerification())
        .rejects.toThrow('Session error')
    })
  })

  describe('getUserWithHousehold', () => {
    it('should return user with household data', async () => {
      // Arrange
      const sessionResponse = {
        data: { session: { user: mockUser } },
        error: null
      }
      mockSupabaseMethod(supabase.auth.getSession, sessionResponse)

      const mockUserData = {
        id: mockUser.id,
        full_name: 'Test User',
        households: { id: 'household-123', name: 'Test Household' }
      }

      const mockFrom = jest.fn(() => ({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            single: jest.fn().mockResolvedValue({ data: mockUserData, error: null })
          }))
        }))
      }))

      supabase.from = mockFrom

      // Act
      const result = await AuthService.getUserWithHousehold()

      // Assert
      expect(result.user).toEqual(mockUserData)
      expect(result.household).toEqual(mockUserData.households)
      expect(mockFrom).toHaveBeenCalledWith('user_profiles')
    })

    it('should return null when no user session', async () => {
      // Arrange
      const noSessionResponse = {
        data: { session: null },
        error: null
      }
      mockSupabaseMethod(supabase.auth.getSession, noSessionResponse)

      // Act
      const result = await AuthService.getUserWithHousehold()

      // Assert
      expect(result.user).toBeNull()
      expect(result.household).toBeNull()
    })

    it('should handle database errors gracefully', async () => {
      // Arrange
      const sessionResponse = {
        data: { session: { user: mockUser } },
        error: null
      }
      mockSupabaseMethod(supabase.auth.getSession, sessionResponse)

      const mockFrom = jest.fn(() => ({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: { message: 'User not found' }
            })
          }))
        }))
      }))

      supabase.from = mockFrom

      // Act
      const result = await AuthService.getUserWithHousehold()

      // Assert
      expect(result.user).toBeNull()
      expect(result.household).toBeNull()
    })
  })
})
