/**
 * Basic test to verify Jest configuration is working
 */
describe('Jest Configuration', () => {
  it('should be able to run tests', () => {
    expect(true).toBe(true)
  })

  it('should have access to testing utilities', () => {
    expect(jest).toBeDefined()
    expect(expect).toBeDefined()
  })

  it('should mock localStorage', () => {
    const mockSetItem = localStorage.setItem as jest.Mock
    localStorage.setItem('test', 'value')
    expect(mockSetItem).toHaveBeenCalledWith('test', 'value')
  })

  it('should mock console methods', () => {
    console.log('test message')
    expect(console.log).toHaveBeenCalledWith('test message')
  })
})
