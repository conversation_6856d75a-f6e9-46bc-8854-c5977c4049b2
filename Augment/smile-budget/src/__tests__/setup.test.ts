/**
 * Basic test to verify Jest configuration is working
 */
describe('Jest Configuration', () => {
  it('should be able to run tests', () => {
    expect(true).toBe(true)
  })

  it('should have access to testing utilities', () => {
    expect(jest).toBeDefined()
    expect(expect).toBeDefined()
  })

  it('should mock localStorage', () => {
    // Test that localStorage methods exist and are functions
    expect(typeof localStorage.setItem).toBe('function')
    expect(typeof localStorage.getItem).toBe('function')
    expect(typeof localStorage.removeItem).toBe('function')
    expect(typeof localStorage.clear).toBe('function')

    // Test that we can call localStorage methods without errors
    localStorage.setItem('test', 'value')
    localStorage.getItem('test')
    localStorage.removeItem('test')
    localStorage.clear()
  })

  it('should mock console methods', () => {
    console.log('test message')
    expect(console.log).toHaveBeenCalledWith('test message')
  })
})
