'use client'

import { useState } from 'react'
import { supabase } from '@/lib/supabase'

export default function SupabaseTest() {
  const [result, setResult] = useState<string>('')
  const [loading, setLoading] = useState(false)

  const testConnection = async () => {
    setLoading(true)
    setResult('')
    
    try {
      console.log('Testing Supabase connection...')
      
      // Test 1: Check basic connection first
      const { data, error } = await supabase.auth.getSession()
        
      console.log('Connection test result:', { data, error })
      
      if (error) {
        setResult(`❌ Auth connection failed: ${error.message}`)
      } else {
        setResult(`✅ Basic connection successful! Supabase client is working.`)
        
        // Test table access
        try {
          console.log('Testing table access...')
          const { data: tableData, error: tableError } = await supabase
            .from('households')
            .select('id')
            .limit(1)
            
          console.log('Table test result:', { tableData, tableError })
          
          if (tableError) {
            setResult(prev => prev + `\n❌ Table access failed: ${tableError.message}`)
          } else {
            setResult(prev => prev + `\n✅ Can access households table! Found ${tableData?.length || 0} records`)
          }
        } catch (tableError) {
          console.error('Table test exception:', tableError)
          setResult(prev => prev + `\n❌ Table test error: ${tableError instanceof Error ? tableError.message : 'Unknown'}`)
        }
      }
      
    } catch (error) {
      console.error('Test error:', error)
      setResult(`❌ Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const testAuth = async () => {
    setLoading(true)
    setResult('')
    
    try {
      const { data: { session }, error } = await supabase.auth.getSession()
      console.log('Auth test result:', { session, error })
      
      if (error) {
        setResult(`❌ Auth failed: ${error.message}`)
      } else if (session) {
        setResult(`✅ User is authenticated: ${session.user.email}`)
      } else {
        setResult(`ℹ️ No active session`)
      }
      
    } catch (error) {
      console.error('Auth test error:', error)
      setResult(`❌ Auth test failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const testCreateHousehold = async () => {
    setLoading(true)
    setResult('')
    
    try {
      console.log('Testing household creation...')
      
      const testHousehold = {
        name: 'Test Family',
        currency_code: 'AUD',
        timezone: 'Australia/Sydney'
      }
      
      const { data, error } = await supabase
        .from('households')
        .insert(testHousehold)
        .select()
        .single()
        
      console.log('Household creation result:', { data, error })
      
      if (error) {
        setResult(`❌ Household creation failed: ${error.message}`)
      } else {
        setResult(`✅ Household created successfully! ID: ${data.id}`)
      }
      
    } catch (error) {
      console.error('Household creation error:', error)
      setResult(`❌ Household creation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const testSignUpFlow = async () => {
    setLoading(true)
    setResult('')
    
    try {
      console.log('Testing signup flow...')
      
      const testEmail = `test${Date.now()}@gmail.com`
      const testPassword = 'testpassword123'
      
      // Test signup
      const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
        email: testEmail,
        password: testPassword,
        options: {
          data: {
            full_name: 'Test User'
          }
        }
      })
      
      console.log('Signup result:', { signUpData, signUpError })
      
      if (signUpError) {
        setResult(`❌ Signup failed: ${signUpError.message}`)
        return
      }
      
      // Check if user is automatically confirmed
      if (signUpData.user && signUpData.session) {
        setResult(`✅ Email confirmation is DISABLED! User signed up and automatically signed in.`)
      } else if (signUpData.user && !signUpData.session) {
        setResult(`⚠️ Email confirmation is ENABLED. User created but not signed in automatically.`)
      } else {
        setResult(`❌ Unexpected signup result`)
      }
      
    } catch (error) {
      console.error('Signup test error:', error)
      setResult(`❌ Signup test failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-6 bg-white rounded-lg border border-gray-200">
      <h3 className="font-semibold text-lg mb-4">Supabase Connection Test</h3>
      
      <div className="space-y-3 mb-4">
        <button
          onClick={testConnection}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {loading ? 'Testing...' : 'Test Database Connection'}
        </button>
        
        <button
          onClick={testAuth}
          disabled={loading}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
        >
          {loading ? 'Testing...' : 'Test Authentication'}
        </button>
        
        <button
          onClick={testCreateHousehold}
          disabled={loading}
          className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
        >
          {loading ? 'Testing...' : 'Test Create Household'}
        </button>
        
        <button
          onClick={testSignUpFlow}
          disabled={loading}
          className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50"
        >
          {loading ? 'Testing...' : 'Test Email Confirmation'}
        </button>
      </div>
      
      {result && (
        <div className="p-3 bg-gray-50 rounded border">
          <p className="text-sm font-mono">{result}</p>
        </div>
      )}
    </div>
  )
}