'use client'

import { useState } from 'react'
import { OnboardingData } from '@/app/onboarding/page'

interface Step4Props {
  data: OnboardingData
  updateData: (updates: Partial<OnboardingData>) => void
  onNext: () => void
  onPrev: () => void
}

interface CategoryTemplate {
  id: string
  name: string
  category_type: 'needs' | 'wants' | 'goals' | 'smile'
  icon_name: string
  description: string
  suggested_percentage: number
  is_recommended: boolean
}

export default function Step4CategorySelection({ data, updateData, onNext, onPrev }: Step4Props) {
  const [selectedCategories, setSelectedCategories] = useState(data.selectedCategories)
  const [activeTab, setActiveTab] = useState<'needs' | 'wants' | 'goals' | 'smile'>('needs')

  // System templates matching our database
  const categoryTemplates: CategoryTemplate[] = [
    // NEEDS Categories
    { id: 'housing', name: 'Housing & Utilities', category_type: 'needs', icon_name: '🏠', description: 'Rent, mortgage, electricity, gas, water, rates', suggested_percentage: 25.00, is_recommended: true },
    { id: 'transport', name: 'Transport', category_type: 'needs', icon_name: '🚗', description: 'Car payments, fuel, public transport, maintenance', suggested_percentage: 10.00, is_recommended: true },
    { id: 'groceries', name: 'Groceries & Food', category_type: 'needs', icon_name: '🛒', description: 'Supermarket shopping, essential food items', suggested_percentage: 8.00, is_recommended: true },
    { id: 'healthcare', name: 'Healthcare', category_type: 'needs', icon_name: '❤️', description: 'Medical expenses, insurance, prescriptions', suggested_percentage: 3.00, is_recommended: true },
    { id: 'insurance', name: 'Insurance', category_type: 'needs', icon_name: '☂️', description: 'Health, car, home, life insurance premiums', suggested_percentage: 2.00, is_recommended: false },
    { id: 'education', name: 'Education', category_type: 'needs', icon_name: '🎓', description: 'School fees, courses, educational materials', suggested_percentage: 2.00, is_recommended: false },
    
    // WANTS Categories  
    { id: 'entertainment', name: 'Entertainment', category_type: 'wants', icon_name: '🎮', description: 'Movies, games, streaming services, hobbies', suggested_percentage: 5.00, is_recommended: true },
    { id: 'dining', name: 'Dining Out', category_type: 'wants', icon_name: '🍽️', description: 'Restaurants, cafes, takeaway food', suggested_percentage: 3.00, is_recommended: true },
    { id: 'personal', name: 'Personal Care', category_type: 'wants', icon_name: '✂️', description: 'Haircuts, beauty, clothing, accessories', suggested_percentage: 4.00, is_recommended: true },
    { id: 'subscriptions', name: 'Subscriptions', category_type: 'wants', icon_name: '📺', description: 'Streaming, magazines, gym memberships', suggested_percentage: 2.00, is_recommended: true },
    { id: 'shopping', name: 'Shopping', category_type: 'wants', icon_name: '🛍️', description: 'Non-essential purchases, gadgets, home decor', suggested_percentage: 3.00, is_recommended: false },
    { id: 'social', name: 'Social Activities', category_type: 'wants', icon_name: '👥', description: 'Socializing, events, recreational activities', suggested_percentage: 3.00, is_recommended: false },
    
    // GOALS Categories
    { id: 'emergency', name: 'Emergency Fund', category_type: 'goals', icon_name: '☂️', description: '6-month expense safety net', suggested_percentage: 10.00, is_recommended: true },
    { id: 'house_deposit', name: 'House Deposit', category_type: 'goals', icon_name: '🏢', description: 'Saving for home purchase deposit', suggested_percentage: 5.00, is_recommended: true },
    { id: 'investments', name: 'Investments', category_type: 'goals', icon_name: '📈', description: 'Shares, ETFs, investment accounts', suggested_percentage: 5.00, is_recommended: true },
    { id: 'retirement', name: 'Retirement', category_type: 'goals', icon_name: '🪑', description: 'Superannuation, retirement savings', suggested_percentage: 3.00, is_recommended: false },
    { id: 'car_replacement', name: 'Car Replacement', category_type: 'goals', icon_name: '🚗', description: 'Saving for next vehicle purchase', suggested_percentage: 2.00, is_recommended: false },
    
    // SMILE Categories
    { id: 'travel', name: 'Travel & Holidays', category_type: 'smile', icon_name: '✈️', description: 'Vacations, weekend trips, travel experiences', suggested_percentage: 3.00, is_recommended: true },
    { id: 'family_experiences', name: 'Family Experiences', category_type: 'smile', icon_name: '🎈', description: 'Activities, outings, special family moments', suggested_percentage: 1.00, is_recommended: true },
    { id: 'treats', name: 'Treats & Surprises', category_type: 'smile', icon_name: '🎁', description: 'Spontaneous purchases, gifts, indulgences', suggested_percentage: 1.00, is_recommended: true }
  ]

  const tabs = [
    { key: 'needs' as const, name: 'NEEDS', icon: '🏠', color: 'needs' },
    { key: 'wants' as const, name: 'WANTS', icon: '🎉', color: 'wants' },
    { key: 'goals' as const, name: 'GOALS', icon: '🎯', color: 'goals' },
    { key: 'smile' as const, name: 'SMILE', icon: '😊', color: 'smile' }
  ]

  const toggleCategory = (categoryId: string, categoryType: 'needs' | 'wants' | 'goals' | 'smile') => {
    const currentSelected = selectedCategories[categoryType]
    const isSelected = currentSelected.includes(categoryId)
    
    let newSelected: string[]
    if (isSelected) {
      newSelected = currentSelected.filter(id => id !== categoryId)
    } else {
      newSelected = [...currentSelected, categoryId]
    }
    
    const updatedCategories = {
      ...selectedCategories,
      [categoryType]: newSelected
    }
    
    setSelectedCategories(updatedCategories)
    updateData({ selectedCategories: updatedCategories })
  }

  const selectRecommended = (categoryType: 'needs' | 'wants' | 'goals' | 'smile') => {
    const recommended = categoryTemplates
      .filter(cat => cat.category_type === categoryType && cat.is_recommended)
      .map(cat => cat.id)
    
    const updatedCategories = {
      ...selectedCategories,
      [categoryType]: recommended
    }
    
    setSelectedCategories(updatedCategories)
    updateData({ selectedCategories: updatedCategories })
  }

  const clearAll = (categoryType: 'needs' | 'wants' | 'goals' | 'smile') => {
    const updatedCategories = {
      ...selectedCategories,
      [categoryType]: []
    }
    
    setSelectedCategories(updatedCategories)
    updateData({ selectedCategories: updatedCategories })
  }

  const getSelectedCount = () => {
    return Object.values(selectedCategories).reduce((total, categories) => total + categories.length, 0)
  }

  const getMinimumRequirements = () => {
    return {
      needs: selectedCategories.needs.length >= 3,
      wants: selectedCategories.wants.length >= 2, 
      goals: selectedCategories.goals.length >= 2,
      smile: selectedCategories.smile.length >= 1
    }
  }

  const canContinue = () => {
    const requirements = getMinimumRequirements()
    return Object.values(requirements).every(req => req)
  }

  const getCurrentTabCategories = () => {
    return categoryTemplates.filter(cat => cat.category_type === activeTab)
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-wants-100 rounded-full mb-4">
          <span className="text-3xl">🎪</span>
        </div>
        <h1 className="font-brand font-bold text-3xl text-gray-800 mb-2">
          Choose your spending categories
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Select the categories that fit your lifestyle. We&apos;ve marked our recommendations, 
          but feel free to customize based on what matters to your family.
        </p>
      </div>

      {/* Progress Summary */}
      <div className="mb-8 p-4 bg-gradient-to-r from-primary-50 to-success-50 border border-primary-200 rounded-xl">
        <div className="flex items-center justify-between">
          <div>
            <p className="font-brand font-medium text-gray-700">
              Categories Selected: {getSelectedCount()}
            </p>
            <p className="text-sm text-gray-600">Minimum 8 categories recommended</p>
          </div>
          <div className="flex items-center gap-2">
            {tabs.map(tab => {
              const count = selectedCategories[tab.key].length
              const requirement = getMinimumRequirements()[tab.key]
              return (
                <div key={tab.key} className="text-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold ${
                    requirement ? 'bg-success-500 text-white' : 'bg-gray-200 text-gray-600'
                  }`}>
                    {count}
                  </div>
                  <p className="text-xs text-gray-500 mt-1">{tab.name}</p>
                </div>
              )
            })}
          </div>
        </div>
      </div>

      {/* Category Type Tabs */}
      <div className="mb-6">
        <div className="flex overflow-x-auto border-b border-gray-200">
          {tabs.map(tab => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key)}
              className={`
                flex items-center gap-2 px-6 py-3 font-brand font-semibold text-sm whitespace-nowrap border-b-2 transition-colors
                ${activeTab === tab.key
                  ? `border-${tab.color}-500 text-${tab.color}-700 bg-${tab.color}-50`
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }
              `}
            >
              <span className="text-lg">{tab.icon}</span>
              {tab.name}
              <span className={`
                ml-1 px-2 py-0.5 rounded-full text-xs font-medium
                ${selectedCategories[tab.key].length > 0
                  ? `bg-${tab.color}-500 text-white`
                  : 'bg-gray-200 text-gray-600'
                }
              `}>
                {selectedCategories[tab.key].length}
              </span>
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h3 className={`font-brand font-semibold text-xl text-${tabs.find(t => t.key === activeTab)?.color}-700`}>
              {tabs.find(t => t.key === activeTab)?.name} Categories
            </h3>
            <p className="text-sm text-gray-600">
              {activeTab === 'needs' && 'Essential expenses you must pay (select at least 3)'}
              {activeTab === 'wants' && 'Nice-to-have lifestyle spending (select at least 2)'}
              {activeTab === 'goals' && 'Future-building savings and investments (select at least 2)'}
              {activeTab === 'smile' && 'Joy and happiness fund (select at least 1)'}
            </p>
          </div>
          
          <div className="flex gap-2">
            <button
              onClick={() => selectRecommended(activeTab)}
              className={`px-3 py-1 rounded-lg text-sm font-medium border border-${tabs.find(t => t.key === activeTab)?.color}-300 text-${tabs.find(t => t.key === activeTab)?.color}-700 hover:bg-${tabs.find(t => t.key === activeTab)?.color}-50`}
            >
              ✨ Select Recommended
            </button>
            <button
              onClick={() => clearAll(activeTab)}
              className="px-3 py-1 rounded-lg text-sm font-medium border border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              Clear All
            </button>
          </div>
        </div>

        {/* Category Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {getCurrentTabCategories().map(category => {
            const isSelected = selectedCategories[activeTab].includes(category.id)
            const tabColor = tabs.find(t => t.key === activeTab)?.color || 'gray'
            
            return (
              <div
                key={category.id}
                onClick={() => toggleCategory(category.id, activeTab)}
                className={`
                  relative p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 hover:shadow-md
                  ${isSelected
                    ? `border-${tabColor}-500 bg-${tabColor}-50 shadow-sm`
                    : 'border-gray-200 bg-white hover:border-gray-300'
                  }
                `}
              >
                {/* Selection Indicator */}
                <div className={`
                  absolute top-3 right-3 w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all
                  ${isSelected
                    ? `border-${tabColor}-500 bg-${tabColor}-500`
                    : 'border-gray-300 bg-white'
                  }
                `}>
                  {isSelected && (
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>

                {/* Recommended Badge */}
                {category.is_recommended && (
                  <div className={`absolute top-3 left-3 px-2 py-0.5 rounded-full text-xs font-medium bg-${tabColor}-500 text-white`}>
                    ✨ Recommended
                  </div>
                )}

                {/* Category Content */}
                <div className={`${category.is_recommended ? 'mt-6' : 'mt-2'}`}>
                  <div className="flex items-center gap-3 mb-2">
                    <span className="text-2xl">{category.icon_name}</span>
                    <h4 className={`font-brand font-semibold text-lg ${isSelected ? `text-${tabColor}-800` : 'text-gray-800'}`}>
                      {category.name}
                    </h4>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-3">
                    {category.description}
                  </p>

                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-500">
                      Suggested: {category.suggested_percentage}%
                    </span>
                    {isSelected && (
                      <span className={`font-medium text-${tabColor}-600`}>
                        ✓ Selected
                      </span>
                    )}
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Requirements Check */}
      <div className="mb-8 p-4 bg-white border-2 border-gray-200 rounded-xl">
        <h4 className="font-brand font-semibold text-lg text-gray-800 mb-3">Requirements Check</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {tabs.map(tab => {
            const requirement = getMinimumRequirements()[tab.key]
            const count = selectedCategories[tab.key].length
            const minCount = tab.key === 'needs' ? 3 : tab.key === 'wants' || tab.key === 'goals' ? 2 : 1
            
            return (
              <div key={tab.key} className="text-center">
                <div className={`
                  w-12 h-12 rounded-full mx-auto mb-2 flex items-center justify-center text-lg
                  ${requirement ? 'bg-success-500 text-white' : 'bg-gray-200 text-gray-600'}
                `}>
                  {tab.icon}
                </div>
                <p className={`font-medium text-sm ${requirement ? 'text-success-600' : 'text-gray-600'}`}>
                  {tab.name}
                </p>
                <p className="text-xs text-gray-500">
                  {count}/{minCount} minimum
                </p>
              </div>
            )
          })}
        </div>
      </div>

      {/* Navigation */}
      <div className="flex justify-between items-center pt-6 border-t border-gray-200">
        <button
          onClick={onPrev}
          className="px-6 py-2 border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50 transition-colors"
        >
          ← Back
        </button>
        
        <button
          onClick={onNext}
          disabled={!canContinue()}
          className={`
            btn-primary px-8 py-3 rounded-lg font-brand font-semibold transition-all duration-200
            ${!canContinue()
              ? 'opacity-50 cursor-not-allowed' 
              : 'hover:transform hover:-translate-y-0.5'
            }
          `}
        >
          Continue to Budget Amounts →
        </button>
      </div>
    </div>
  )
}