'use client'

import { useState, useEffect } from 'react'
import { OnboardingData } from '@/app/onboarding/page'
import { DatabaseService, AuthService, PendingUser } from '@/lib/database'
import { useRouter } from 'next/navigation'
import SupabaseTest from '@/components/SupabaseTest'

interface Step6Props {
  data: OnboardingData
  updateData: (updates: Partial<OnboardingData>) => void
  onPrev: () => void
  goToStep: (step: number) => void
}

interface CategoryTemplate {
  id: string
  name: string
  category_type: 'needs' | 'wants' | 'goals' | 'smile'
  icon_name: string
  description: string
  suggested_percentage: number
  is_recommended: boolean
}

export default function Step6ReviewLaunch({ data, updateData, onPrev, goToStep }: Step6Props) {
  const [isLaunching, setIsLaunching] = useState(false)
  const [launchError, setLaunchError] = useState('')
  const [pendingUser, setPendingUser] = useState<PendingUser | null>(null)
  const router = useRouter()

  // Check for pending user data on component mount
  useEffect(() => {
    const checkPendingUser = () => {
      try {
        console.log('=== CHECKING PENDING USER IN STEP 6 ===')
        const pendingUserData = AuthService.getPendingUser()
        console.log('Pending user data:', pendingUserData)

        if (pendingUserData) {
          setPendingUser(pendingUserData)
          console.log('✅ Found pending user:', pendingUserData.email)
        } else {
          console.log('❌ No pending user found')
          // Check if user is already authenticated (fallback)
          AuthService.getCurrentUser().then(({ user }) => {
            if (user && user.email) {
              console.log('✅ User is already authenticated:', user.email)
              setPendingUser({
                userId: user.id,
                email: user.email,
                fullName: user.user_metadata?.full_name || data.yourName,
                needsVerification: false,
                createdAt: new Date().toISOString()
              })
            }
          })
        }
      } catch (error) {
        console.error('Error checking pending user:', error)
      }
    }

    checkPendingUser()
  }, [data.yourName])

  // System templates matching our database
  const categoryTemplates: CategoryTemplate[] = [
    // NEEDS Categories
    { id: 'housing', name: 'Housing & Utilities', category_type: 'needs', icon_name: '🏠', description: 'Rent, mortgage, electricity, gas, water, rates', suggested_percentage: 25.00, is_recommended: true },
    { id: 'transport', name: 'Transport', category_type: 'needs', icon_name: '🚗', description: 'Car payments, fuel, public transport, maintenance', suggested_percentage: 10.00, is_recommended: true },
    { id: 'groceries', name: 'Groceries & Food', category_type: 'needs', icon_name: '🛒', description: 'Supermarket shopping, essential food items', suggested_percentage: 8.00, is_recommended: true },
    { id: 'healthcare', name: 'Healthcare', category_type: 'needs', icon_name: '❤️', description: 'Medical expenses, insurance, prescriptions', suggested_percentage: 3.00, is_recommended: true },
    { id: 'insurance', name: 'Insurance', category_type: 'needs', icon_name: '☂️', description: 'Health, car, home, life insurance premiums', suggested_percentage: 2.00, is_recommended: false },
    { id: 'education', name: 'Education', category_type: 'needs', icon_name: '🎓', description: 'School fees, courses, educational materials', suggested_percentage: 2.00, is_recommended: false },
    
    // WANTS Categories  
    { id: 'entertainment', name: 'Entertainment', category_type: 'wants', icon_name: '🎮', description: 'Movies, games, streaming services, hobbies', suggested_percentage: 5.00, is_recommended: true },
    { id: 'dining', name: 'Dining Out', category_type: 'wants', icon_name: '🍽️', description: 'Restaurants, cafes, takeaway food', suggested_percentage: 3.00, is_recommended: true },
    { id: 'personal', name: 'Personal Care', category_type: 'wants', icon_name: '✂️', description: 'Haircuts, beauty, clothing, accessories', suggested_percentage: 4.00, is_recommended: true },
    { id: 'subscriptions', name: 'Subscriptions', category_type: 'wants', icon_name: '📺', description: 'Streaming, magazines, gym memberships', suggested_percentage: 2.00, is_recommended: true },
    { id: 'shopping', name: 'Shopping', category_type: 'wants', icon_name: '🛍️', description: 'Non-essential purchases, gadgets, home decor', suggested_percentage: 3.00, is_recommended: false },
    { id: 'social', name: 'Social Activities', category_type: 'wants', icon_name: '👥', description: 'Socializing, events, recreational activities', suggested_percentage: 3.00, is_recommended: false },
    
    // GOALS Categories
    { id: 'emergency', name: 'Emergency Fund', category_type: 'goals', icon_name: '☂️', description: '6-month expense safety net', suggested_percentage: 10.00, is_recommended: true },
    { id: 'house_deposit', name: 'House Deposit', category_type: 'goals', icon_name: '🏢', description: 'Saving for home purchase deposit', suggested_percentage: 5.00, is_recommended: true },
    { id: 'investments', name: 'Investments', category_type: 'goals', icon_name: '📈', description: 'Shares, ETFs, investment accounts', suggested_percentage: 5.00, is_recommended: true },
    { id: 'retirement', name: 'Retirement', category_type: 'goals', icon_name: '🪑', description: 'Superannuation, retirement savings', suggested_percentage: 3.00, is_recommended: false },
    { id: 'car_replacement', name: 'Car Replacement', category_type: 'goals', icon_name: '🚗', description: 'Saving for next vehicle purchase', suggested_percentage: 2.00, is_recommended: false },
    
    // SMILE Categories
    { id: 'travel', name: 'Travel & Holidays', category_type: 'smile', icon_name: '✈️', description: 'Vacations, weekend trips, travel experiences', suggested_percentage: 3.00, is_recommended: true },
    { id: 'family_experiences', name: 'Family Experiences', category_type: 'smile', icon_name: '🎈', description: 'Activities, outings, special family moments', suggested_percentage: 1.00, is_recommended: true },
    { id: 'treats', name: 'Treats & Surprises', category_type: 'smile', icon_name: '🎁', description: 'Spontaneous purchases, gifts, indulgences', suggested_percentage: 1.00, is_recommended: true }
  ]

  const getCategoryTemplate = (categoryId: string) => {
    return categoryTemplates.find(t => t.id === categoryId)
  }

  const getCategoryAmount = (categoryId: string) => {
    const categoryAmount = data.categoryAmounts?.find(ca => ca.categoryId === categoryId)
    return categoryAmount?.amount || 0
  }

  const getTotalForType = (categoryType: 'needs' | 'wants' | 'goals' | 'smile') => {
    return data.selectedCategories[categoryType]
      .reduce((total, categoryId) => total + getCategoryAmount(categoryId), 0)
  }

  const getAllocatedForType = (categoryType: 'needs' | 'wants' | 'goals' | 'smile') => {
    const typeAllocation = data.allocations[categoryType as keyof typeof data.allocations]
    return Math.round((data.totalMonthlyIncome * typeAllocation) / 100)
  }

  const handleLaunch = async () => {
    setIsLaunching(true)
    setLaunchError('')

    try {
      console.log('=== LAUNCH BUTTON CLICKED ===')

      if (!pendingUser) {
        throw new Error('No user data found. Please go back and complete signup.')
      }

      console.log('✅ Creating budget for pending user:', pendingUser.email)

      // Create household and budget data for unverified user
      await createBudgetFromOnboarding(pendingUser.email, pendingUser.userId)

    } catch (error) {
      console.error('Launch error:', {
        error,
        message: error instanceof Error ? error.message : 'Unknown error',
        details: error
      })
      setLaunchError(`Failed to create budget: ${error instanceof Error ? error.message : 'Unknown error'}`)
      setIsLaunching(false)
    }
  }



  const createBudgetFromOnboarding = async (userEmail: string, userId: string) => {
    try {
      // Create household and all budget data
      await DatabaseService.createHouseholdFromOnboarding(
        data,
        userEmail,
        userId
      )
      
      // Mark onboarding as complete
      updateData({ isComplete: true })
      
      // Redirect to dashboard
      router.push('/dashboard')
      
    } catch (error) {
      console.error('Error creating budget:', {
        error,
        message: error instanceof Error ? error.message : 'Unknown error',
        details: error
      })
      throw new Error(`Failed to create budget: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  const categoryTypes = [
    { key: 'needs' as const, name: 'NEEDS', icon: '🏠', color: 'needs' },
    { key: 'wants' as const, name: 'WANTS', icon: '🎉', color: 'wants' },
    { key: 'goals' as const, name: 'GOALS', icon: '🎯', color: 'goals' },
    { key: 'smile' as const, name: 'SMILE', icon: '😊', color: 'smile' }
  ]

  return (
    <div className="max-w-5xl mx-auto">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-success-100 rounded-full mb-4">
          <span className="text-3xl">🎉</span>
        </div>
        <h1 className="font-brand font-bold text-3xl text-gray-800 mb-2">
          Your Smile Budget is Ready!
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Review your complete budget setup below. Everything looks great - 
          you&apos;re ready to start your journey to financial happiness!
        </p>
      </div>

      {/* Household Summary */}
      <div className="mb-8 p-6 bg-gradient-to-r from-primary-50 to-success-50 border border-primary-200 rounded-xl">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <span className="text-3xl">👨‍👩‍👧‍👦</span>
            <div>
              <h3 className="font-brand font-semibold text-xl text-gray-800">{data.householdName}</h3>
              <p className="text-gray-600">
                {data.householdComposition.adults} adult{data.householdComposition.adults !== 1 ? 's' : ''}, 
                {data.householdComposition.children} child{data.householdComposition.children !== 1 ? 'ren' : ''}
                {data.householdComposition.pets ? ', with pets' : ''}
              </p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-600">Monthly Income</p>
            <p className="font-data font-bold text-2xl text-primary-700">
              ${data.totalMonthlyIncome.toLocaleString()}
            </p>
          </div>
        </div>
      </div>

      {/* Income Sources */}
      <div className="mb-8 bg-white rounded-xl shadow-soft p-6">
        <h3 className="font-brand font-semibold text-xl text-gray-800 mb-4 flex items-center gap-2">
          💰 Income Sources
          <button 
            onClick={() => goToStep(2)}
            className="text-sm px-3 py-1 rounded-lg border border-primary-300 text-primary-700 hover:bg-primary-50"
          >
            Edit
          </button>
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {data.incomeSourcess.map((income, index) => (
            <div key={income.id || index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="font-medium text-gray-800">{income.name}</p>
                <p className="text-sm text-gray-600 capitalize">{income.type} • {income.frequency}</p>
              </div>
              <p className="font-data font-semibold text-gray-800">
                ${income.amount.toLocaleString()}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Budget Allocation Overview */}
      <div className="mb-8 bg-white rounded-xl shadow-soft p-6">
        <h3 className="font-brand font-semibold text-xl text-gray-800 mb-4 flex items-center gap-2">
          🎯 Budget Allocation
          <button 
            onClick={() => goToStep(3)}
            className="text-sm px-3 py-1 rounded-lg border border-primary-300 text-primary-700 hover:bg-primary-50"
          >
            Edit
          </button>
        </h3>
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {categoryTypes.map(type => {
            const allocated = getAllocatedForType(type.key)
            const percentage = data.allocations[type.key]
            
            return (
              <div key={type.key} className={`${type.color}-theme rounded-lg p-4`}>
                <div className="flex items-center gap-2 mb-2">
                  <span className={`text-${type.color}-600`}>{type.icon}</span>
                  <span className={`font-brand font-semibold text-sm text-${type.color}-700`}>{type.name}</span>
                </div>
                <p className={`font-data font-semibold text-lg text-${type.color}-800`}>
                  ${allocated.toLocaleString()}
                </p>
                <p className={`text-xs text-${type.color}-600`}>{percentage}% of income</p>
              </div>
            )
          })}
        </div>
      </div>

      {/* Category Details */}
      <div className="mb-8 bg-white rounded-xl shadow-soft p-6">
        <h3 className="font-brand font-semibold text-xl text-gray-800 mb-6 flex items-center gap-2">
          📊 Category Breakdown
          <button 
            onClick={() => goToStep(4)}
            className="text-sm px-3 py-1 rounded-lg border border-primary-300 text-primary-700 hover:bg-primary-50 mr-2"
          >
            Edit Categories
          </button>
          <button 
            onClick={() => goToStep(5)}
            className="text-sm px-3 py-1 rounded-lg border border-primary-300 text-primary-700 hover:bg-primary-50"
          >
            Edit Amounts
          </button>
        </h3>
        
        <div className="space-y-6">
          {categoryTypes.map(type => {
            const categories = data.selectedCategories[type.key]
            const allocated = getAllocatedForType(type.key)
            const assigned = getTotalForType(type.key)
            const remaining = allocated - assigned
            
            return (
              <div key={type.key} className={`border-2 border-${type.color}-200 bg-${type.color}-50 rounded-lg p-4`}>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <span className="text-2xl">{type.icon}</span>
                    <div>
                      <h4 className={`font-brand font-semibold text-lg text-${type.color}-800`}>
                        {type.name}
                      </h4>
                      <p className="text-sm text-gray-600">
                        {categories.length} categor{categories.length !== 1 ? 'ies' : 'y'} selected
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`font-data font-semibold text-xl text-${type.color}-800`}>
                      ${assigned.toLocaleString()}
                    </p>
                    <p className="text-sm text-gray-600">of ${allocated.toLocaleString()}</p>
                    {Math.abs(remaining) > 5 && (
                      <p className={`text-xs font-medium ${remaining > 0 ? 'text-amber-600' : 'text-red-600'}`}>
                        {remaining > 0 ? `$${remaining} unassigned` : `$${Math.abs(remaining)} over`}
                      </p>
                    )}
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {categories.map(categoryId => {
                    const template = getCategoryTemplate(categoryId)
                    const amount = getCategoryAmount(categoryId)
                    
                    if (!template) return null
                    
                    return (
                      <div key={categoryId} className="bg-white rounded-lg p-3 border border-gray-200">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-lg">{template.icon_name}</span>
                          <p className="font-medium text-gray-800 text-sm">{template.name}</p>
                        </div>
                        <p className="font-data font-semibold text-gray-700">${amount.toLocaleString()}</p>
                      </div>
                    )
                  })}
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Debug Section - Remove after testing */}
      <div className="mb-8">
        <SupabaseTest />
      </div>

      {/* Smile Philosophy Reminder */}
      <div className="mb-8 p-6 bg-gradient-to-r from-smile-50 to-primary-50 border border-smile-200 rounded-xl">
        <div className="flex items-start gap-4">
          <span className="text-3xl">😊</span>
          <div>
            <h4 className="font-brand font-semibold text-lg text-smile-800 mb-2">
              Welcome to the Smile Philosophy!
            </h4>
            <p className="text-smile-700 mb-3">
              You&apos;ve just created a budget that balances your essential needs, desired lifestyle, 
              future goals, and most importantly - your family&apos;s happiness and joy.
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="text-center">
                <div className="w-12 h-12 bg-needs-500 rounded-full flex items-center justify-center text-white mb-2 mx-auto">
                  🏠
                </div>
                <p className="font-medium text-needs-700">NEEDS</p>
                <p className="text-gray-600">Security & stability</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-wants-500 rounded-full flex items-center justify-center text-white mb-2 mx-auto">
                  🎉
                </div>
                <p className="font-medium text-wants-700">WANTS</p>
                <p className="text-gray-600">Lifestyle & comfort</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-goals-500 rounded-full flex items-center justify-center text-white mb-2 mx-auto">
                  🎯
                </div>
                <p className="font-medium text-goals-700">GOALS</p>
                <p className="text-gray-600">Future building</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-smile-500 rounded-full flex items-center justify-center text-white mb-2 mx-auto">
                  😊
                </div>
                <p className="font-medium text-smile-700">SMILE</p>
                <p className="text-gray-600">Joy & happiness</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {launchError && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-700 text-sm">{launchError}</p>
        </div>
      )}

      {/* No User Warning */}
      {!pendingUser && (
        <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-700 text-sm">
            ⚠️ No user data found. Please go back to the landing page and complete signup first.
          </p>
        </div>
      )}



      {/* Navigation */}
      <div className="flex justify-between items-center pt-6 border-t border-gray-200">
        <button
          onClick={onPrev}
          className="px-6 py-2 border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50 transition-colors"
        >
          ← Back to Edit
        </button>
        
        <button
          onClick={handleLaunch}
          disabled={isLaunching}
          className={`
            btn-primary px-8 py-3 rounded-lg font-brand font-semibold transition-all duration-200
            ${isLaunching
              ? 'opacity-50 cursor-not-allowed' 
              : 'hover:transform hover:-translate-y-0.5'
            }
          `}
        >
          {isLaunching ? (
            <>
              <span className="animate-spin inline-block mr-2">⏳</span>
              Creating Your Budget...
            </>
          ) : (
            '🚀 Launch My Smile Budget!'
          )}
        </button>
      </div>
    </div>
  )
}