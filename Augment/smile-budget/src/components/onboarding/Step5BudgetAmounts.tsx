'use client'

import { useState, useEffect } from 'react'
import { OnboardingData } from '@/app/onboarding/page'

interface Step5Props {
  data: OnboardingData
  updateData: (updates: Partial<OnboardingData>) => void
  onNext: () => void
  onPrev: () => void
}

interface CategoryTemplate {
  id: string
  name: string
  category_type: 'needs' | 'wants' | 'goals' | 'smile'
  icon_name: string
  description: string
  suggested_percentage: number
  is_recommended: boolean
}

interface CategoryAmount {
  categoryId: string
  amount: number
}

// System templates matching our database
const categoryTemplates: CategoryTemplate[] = [
  // NEEDS Categories
  { id: 'housing', name: 'Housing & Utilities', category_type: 'needs', icon_name: '🏠', description: 'Rent, mortgage, electricity, gas, water, rates', suggested_percentage: 25.00, is_recommended: true },
  { id: 'transport', name: 'Transport', category_type: 'needs', icon_name: '🚗', description: 'Car payments, fuel, public transport, maintenance', suggested_percentage: 10.00, is_recommended: true },
  { id: 'groceries', name: 'Groceries & Food', category_type: 'needs', icon_name: '🛒', description: 'Supermarket shopping, essential food items', suggested_percentage: 8.00, is_recommended: true },
  { id: 'healthcare', name: 'Healthcare', category_type: 'needs', icon_name: '❤️', description: 'Medical expenses, insurance, prescriptions', suggested_percentage: 3.00, is_recommended: true },
  { id: 'insurance', name: 'Insurance', category_type: 'needs', icon_name: '☂️', description: 'Health, car, home, life insurance premiums', suggested_percentage: 2.00, is_recommended: false },
  { id: 'education', name: 'Education', category_type: 'needs', icon_name: '🎓', description: 'School fees, courses, educational materials', suggested_percentage: 2.00, is_recommended: false },
  
  // WANTS Categories  
  { id: 'entertainment', name: 'Entertainment', category_type: 'wants', icon_name: '🎮', description: 'Movies, games, streaming services, hobbies', suggested_percentage: 5.00, is_recommended: true },
  { id: 'dining', name: 'Dining Out', category_type: 'wants', icon_name: '🍽️', description: 'Restaurants, cafes, takeaway food', suggested_percentage: 3.00, is_recommended: true },
  { id: 'personal', name: 'Personal Care', category_type: 'wants', icon_name: '✂️', description: 'Haircuts, beauty, clothing, accessories', suggested_percentage: 4.00, is_recommended: true },
  { id: 'subscriptions', name: 'Subscriptions', category_type: 'wants', icon_name: '📺', description: 'Streaming, magazines, gym memberships', suggested_percentage: 2.00, is_recommended: true },
  { id: 'shopping', name: 'Shopping', category_type: 'wants', icon_name: '🛍️', description: 'Non-essential purchases, gadgets, home decor', suggested_percentage: 3.00, is_recommended: false },
  { id: 'social', name: 'Social Activities', category_type: 'wants', icon_name: '👥', description: 'Socializing, events, recreational activities', suggested_percentage: 3.00, is_recommended: false },
  
  // GOALS Categories
  { id: 'emergency', name: 'Emergency Fund', category_type: 'goals', icon_name: '☂️', description: '6-month expense safety net', suggested_percentage: 10.00, is_recommended: true },
  { id: 'house_deposit', name: 'House Deposit', category_type: 'goals', icon_name: '🏢', description: 'Saving for home purchase deposit', suggested_percentage: 5.00, is_recommended: true },
  { id: 'investments', name: 'Investments', category_type: 'goals', icon_name: '📈', description: 'Shares, ETFs, investment accounts', suggested_percentage: 5.00, is_recommended: true },
  { id: 'retirement', name: 'Retirement', category_type: 'goals', icon_name: '🪑', description: 'Superannuation, retirement savings', suggested_percentage: 3.00, is_recommended: false },
  { id: 'car_replacement', name: 'Car Replacement', category_type: 'goals', icon_name: '🚗', description: 'Saving for next vehicle purchase', suggested_percentage: 2.00, is_recommended: false },
  
  // SMILE Categories
  { id: 'travel', name: 'Travel & Holidays', category_type: 'smile', icon_name: '✈️', description: 'Vacations, weekend trips, travel experiences', suggested_percentage: 3.00, is_recommended: true },
  { id: 'family_experiences', name: 'Family Experiences', category_type: 'smile', icon_name: '🎈', description: 'Activities, outings, special family moments', suggested_percentage: 1.00, is_recommended: true },
  { id: 'treats', name: 'Treats & Surprises', category_type: 'smile', icon_name: '🎁', description: 'Spontaneous purchases, gifts, indulgences', suggested_percentage: 1.00, is_recommended: true }
]

export default function Step5BudgetAmounts({ data, updateData, onNext, onPrev }: Step5Props) {
  const [categoryAmounts, setCategoryAmounts] = useState<Record<string, number>>({})
  const [activeTab, setActiveTab] = useState<'needs' | 'wants' | 'goals' | 'smile'>('needs')

  const tabs = [
    { key: 'needs' as const, name: 'NEEDS', icon: '🏠', color: 'needs' },
    { key: 'wants' as const, name: 'WANTS', icon: '🎉', color: 'wants' },
    { key: 'goals' as const, name: 'GOALS', icon: '🎯', color: 'goals' },
    { key: 'smile' as const, name: 'SMILE', icon: '😊', color: 'smile' }
  ]

  // Initialize category amounts based on selected categories and suggested percentages
  useEffect(() => {
    const initialAmounts: Record<string, number> = {}
    
    Object.entries(data.selectedCategories).forEach(([_categoryType, categoryIds]) => {
      categoryIds.forEach(categoryId => {
        const template = categoryTemplates.find(t => t.id === categoryId)
        if (template) {
          // Calculate suggested amount based on template percentage relative to total income
          const suggestedAmount = Math.round((data.totalMonthlyIncome * template.suggested_percentage) / 100)
          initialAmounts[categoryId] = suggestedAmount
        }
      })
    })
    
    setCategoryAmounts(initialAmounts)
  }, [data.selectedCategories, data.allocations, data.totalMonthlyIncome])

  const updateCategoryAmount = (categoryId: string, amount: number) => {
    const newAmounts = {
      ...categoryAmounts,
      [categoryId]: Math.max(0, amount)
    }
    setCategoryAmounts(newAmounts)
  }

  const getSelectedCategoriesForType = (categoryType: 'needs' | 'wants' | 'goals' | 'smile') => {
    return data.selectedCategories[categoryType]
      .map(id => categoryTemplates.find(t => t.id === id))
      .filter(Boolean) as CategoryTemplate[]
  }

  const getTotalForType = (categoryType: 'needs' | 'wants' | 'goals' | 'smile') => {
    return data.selectedCategories[categoryType]
      .reduce((total, categoryId) => total + (categoryAmounts[categoryId] || 0), 0)
  }

  const getAllocatedForType = (categoryType: 'needs' | 'wants' | 'goals' | 'smile') => {
    const typeAllocation = data.allocations[categoryType as keyof typeof data.allocations]
    return Math.round((data.totalMonthlyIncome * typeAllocation) / 100)
  }

  const getRemainingForType = (categoryType: 'needs' | 'wants' | 'goals' | 'smile') => {
    return getAllocatedForType(categoryType) - getTotalForType(categoryType)
  }

  const getBalanceStatus = (categoryType: 'needs' | 'wants' | 'goals' | 'smile') => {
    const remaining = getRemainingForType(categoryType)
    if (remaining > 0) return { status: 'under', color: 'amber', message: `$${remaining} remaining` }
    if (remaining < 0) return { status: 'over', color: 'red', message: `$${Math.abs(remaining)} over budget` }
    return { status: 'perfect', color: 'success', message: 'Perfect balance!' }
  }

  const distributeRemainingForType = (categoryType: 'needs' | 'wants' | 'goals' | 'smile') => {
    const remaining = getRemainingForType(categoryType)
    const categories = getSelectedCategoriesForType(categoryType)
    
    if (remaining <= 0 || categories.length === 0) return
    
    const amountPerCategory = Math.floor(remaining / categories.length)
    const leftover = remaining % categories.length
    
    const newAmounts = { ...categoryAmounts }
    
    categories.forEach((category, index) => {
      const extraAmount = index < leftover ? 1 : 0
      newAmounts[category.id] = (newAmounts[category.id] || 0) + amountPerCategory + extraAmount
    })
    
    setCategoryAmounts(newAmounts)
  }

  const canContinue = () => {
    return tabs.every(tab => {
      const remaining = getRemainingForType(tab.key)
      return Math.abs(remaining) <= 5 // Allow small rounding differences
    })
  }

  const handleNext = () => {
    // Convert category amounts to the format expected by the data structure
    const categoryAmountsList: CategoryAmount[] = Object.entries(categoryAmounts).map(([categoryId, amount]) => ({
      categoryId,
      amount
    }))
    
    updateData({ categoryAmounts: categoryAmountsList })
    onNext()
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-100 rounded-full mb-4">
          <span className="text-3xl">💰</span>
        </div>
        <h1 className="font-brand font-bold text-3xl text-gray-800 mb-2">
          Set your budget amounts
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Now let&apos;s assign specific dollar amounts to each category you&apos;ve selected. 
          We&apos;ve suggested amounts based on your income and budget allocation.
        </p>
      </div>

      {/* Budget Summary */}
      <div className="mb-8 p-4 bg-gradient-to-r from-primary-50 to-success-50 border border-primary-200 rounded-xl">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {tabs.map(tab => {
            const allocated = getAllocatedForType(tab.key)
            const total = getTotalForType(tab.key)
            const balance = getBalanceStatus(tab.key)
            
            return (
              <div key={tab.key} className="text-center">
                <p className={`font-brand font-medium text-${tab.color}-700`}>{tab.name}</p>
                <p className="font-data font-semibold text-lg text-gray-800">${allocated.toLocaleString()}</p>
                <p className={`text-xs font-medium text-${balance.color}-600`}>
                  ${total.toLocaleString()} assigned
                </p>
              </div>
            )
          })}
        </div>
      </div>

      {/* Category Type Tabs */}
      <div className="mb-6">
        <div className="flex overflow-x-auto border-b border-gray-200">
          {tabs.map(tab => {
            const balance = getBalanceStatus(tab.key)
            
            return (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                className={`
                  flex items-center gap-2 px-6 py-3 font-brand font-semibold text-sm whitespace-nowrap border-b-2 transition-colors
                  ${activeTab === tab.key
                    ? `border-${tab.color}-500 text-${tab.color}-700 bg-${tab.color}-50`
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <span className="text-lg">{tab.icon}</span>
                {tab.name}
                <span className={`
                  ml-1 px-2 py-0.5 rounded-full text-xs font-medium
                  ${balance.status === 'perfect' ? 'bg-success-500 text-white' :
                    balance.status === 'over' ? 'bg-red-500 text-white' :
                    'bg-amber-500 text-white'
                  }
                `}>
                  ${getTotalForType(tab.key).toLocaleString()}
                </span>
              </button>
            )
          })}
        </div>
      </div>

      {/* Tab Content */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h3 className={`font-brand font-semibold text-xl text-${tabs.find(t => t.key === activeTab)?.color}-700`}>
              {tabs.find(t => t.key === activeTab)?.name} Budget
            </h3>
            <p className="text-sm text-gray-600">
              Allocated: ${getAllocatedForType(activeTab).toLocaleString()} • 
              Assigned: ${getTotalForType(activeTab).toLocaleString()}
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <div className={`text-center px-3 py-2 rounded-lg border-2 ${
              getBalanceStatus(activeTab).status === 'perfect' ? 'border-success-200 bg-success-50' :
              getBalanceStatus(activeTab).status === 'over' ? 'border-red-200 bg-red-50' :
              'border-amber-200 bg-amber-50'
            }`}>
              <p className={`text-xs font-medium text-${getBalanceStatus(activeTab).color}-600`}>
                {getBalanceStatus(activeTab).message}
              </p>
            </div>
            
            {getRemainingForType(activeTab) > 0 && (
              <button
                onClick={() => distributeRemainingForType(activeTab)}
                className={`px-3 py-1 rounded-lg text-sm font-medium border border-${tabs.find(t => t.key === activeTab)?.color}-300 text-${tabs.find(t => t.key === activeTab)?.color}-700 hover:bg-${tabs.find(t => t.key === activeTab)?.color}-50`}
              >
                💫 Distribute Remaining
              </button>
            )}
          </div>
        </div>

        {/* Category Amount Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {getSelectedCategoriesForType(activeTab).map(category => {
            const currentAmount = categoryAmounts[category.id] || 0
            const suggestedAmount = Math.round((data.totalMonthlyIncome * category.suggested_percentage) / 100)
            const tabColor = tabs.find(t => t.key === activeTab)?.color || 'gray'
            
            return (
              <div
                key={category.id}
                className={`border-2 border-${tabColor}-200 bg-${tabColor}-50 rounded-xl p-4`}
              >
                {/* Category Header */}
                <div className="flex items-center gap-3 mb-3">
                  <span className="text-2xl">{category.icon_name}</span>
                  <div className="flex-1">
                    <h4 className={`font-brand font-semibold text-lg text-${tabColor}-800`}>
                      {category.name}
                    </h4>
                    <p className="text-sm text-gray-600">{category.description}</p>
                  </div>
                </div>

                {/* Amount Input */}
                <div className="mb-3">
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">$</span>
                    <input
                      type="number"
                      min="0"
                      step="10"
                      value={currentAmount}
                      onChange={(e) => updateCategoryAmount(category.id, parseInt(e.target.value) || 0)}
                      className={`
                        w-full pl-8 pr-4 py-3 border-2 rounded-lg font-data font-semibold text-lg
                        border-${tabColor}-300 bg-white text-gray-800
                        focus:border-${tabColor}-500 focus:ring-2 focus:ring-${tabColor}-200 focus:outline-none
                        transition-all duration-200
                      `}
                      placeholder="0"
                    />
                  </div>
                </div>

                {/* Quick Amount Buttons */}
                <div className="flex gap-2 mb-3">
                  <button
                    onClick={() => updateCategoryAmount(category.id, suggestedAmount)}
                    className={`flex-1 px-3 py-1 rounded-lg text-xs font-medium border border-${tabColor}-300 text-${tabColor}-700 hover:bg-${tabColor}-100`}
                  >
                    Suggested: ${suggestedAmount}
                  </button>
                  <button
                    onClick={() => updateCategoryAmount(category.id, 0)}
                    className="px-3 py-1 rounded-lg text-xs font-medium border border-gray-300 text-gray-600 hover:bg-gray-100"
                  >
                    Clear
                  </button>
                </div>

                {/* Amount Comparison */}
                <div className="text-xs text-gray-600">
                  {currentAmount === suggestedAmount ? (
                    <p className="text-success-600 font-medium">✓ Matches suggestion</p>
                  ) : currentAmount > suggestedAmount ? (
                    <p className="text-amber-600">⬆ ${currentAmount - suggestedAmount} above suggestion</p>
                  ) : currentAmount > 0 ? (
                    <p className="text-blue-600">⬇ ${suggestedAmount - currentAmount} below suggestion</p>
                  ) : (
                    <p className="text-gray-500">No amount set</p>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Balance Check */}
      <div className="mb-8 p-4 bg-white border-2 border-gray-200 rounded-xl">
        <h4 className="font-brand font-semibold text-lg text-gray-800 mb-4">Budget Balance Check</h4>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {tabs.map(tab => {
            const balance = getBalanceStatus(tab.key)
            const allocated = getAllocatedForType(tab.key)
            const assigned = getTotalForType(tab.key)
            
            return (
              <div key={tab.key} className="text-center">
                <div className={`
                  w-12 h-12 rounded-full mx-auto mb-2 flex items-center justify-center text-lg
                  ${balance.status === 'perfect' ? 'bg-success-500 text-white' :
                    balance.status === 'over' ? 'bg-red-500 text-white' :
                    'bg-amber-500 text-white'
                  }
                `}>
                  {tab.icon}
                </div>
                <p className={`font-medium text-sm ${
                  balance.status === 'perfect' ? 'text-success-600' :
                  balance.status === 'over' ? 'text-red-600' :
                  'text-amber-600'
                }`}>
                  {tab.name}
                </p>
                <p className="text-xs text-gray-600">
                  ${assigned.toLocaleString()} / ${allocated.toLocaleString()}
                </p>
                <p className={`text-xs font-medium ${
                  balance.status === 'perfect' ? 'text-success-600' :
                  balance.status === 'over' ? 'text-red-600' :
                  'text-amber-600'
                }`}>
                  {balance.message}
                </p>
              </div>
            )
          })}
        </div>
      </div>

      {/* Navigation */}
      <div className="flex justify-between items-center pt-6 border-t border-gray-200">
        <button
          onClick={onPrev}
          className="px-6 py-2 border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50 transition-colors"
        >
          ← Back
        </button>
        
        <button
          onClick={handleNext}
          disabled={!canContinue()}
          className={`
            btn-primary px-8 py-3 rounded-lg font-brand font-semibold transition-all duration-200
            ${!canContinue()
              ? 'opacity-50 cursor-not-allowed' 
              : 'hover:transform hover:-translate-y-0.5'
            }
          `}
        >
          Continue to Review →
        </button>
      </div>
    </div>
  )
}