'use client'

interface Step {
  number: number
  title: string
  description: string
}

interface OnboardingLayoutProps {
  currentStep: number
  steps: Step[]
  onStepClick: (step: number) => void
  children: React.ReactNode
}

export default function OnboardingLayout({ 
  currentStep, 
  steps, 
  onStepClick, 
  children 
}: OnboardingLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-4 py-4 sm:px-6">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">😊</span>
            </div>
            <div>
              <h1 className="font-brand font-bold text-xl text-primary-700">Smile Budget</h1>
              <p className="text-xs text-gray-500">Let&apos;s set up your family budget</p>
            </div>
          </div>
          
          <div className="text-right">
            <p className="font-brand font-medium text-sm text-gray-700">
              Step {currentStep} of {steps.length}
            </p>
            <p className="text-xs text-gray-500">{steps[currentStep - 1]?.title}</p>
          </div>
        </div>
      </header>

      {/* Step Progress Bar */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 py-6 sm:px-6">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.number} className="flex items-center">
                {/* Step Circle */}
                <div
                  className={`
                    flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-200 cursor-pointer
                    ${step.number === currentStep
                      ? 'bg-primary-600 border-primary-600 text-white'
                      : step.number < currentStep
                        ? 'bg-success-500 border-success-500 text-white'
                        : 'bg-gray-100 border-gray-300 text-gray-400'
                    }
                  `}
                  onClick={() => step.number <= currentStep && onStepClick(step.number)}
                >
                  {step.number < currentStep ? (
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <span className="font-brand font-semibold text-sm">{step.number}</span>
                  )}
                </div>

                {/* Step Label */}
                <div className="ml-3 hidden sm:block">
                  <p className={`font-brand font-medium text-sm ${
                    step.number === currentStep ? 'text-primary-700' : 
                    step.number < currentStep ? 'text-success-600' : 'text-gray-500'
                  }`}>
                    {step.title}
                  </p>
                  <p className="text-xs text-gray-400">{step.description}</p>
                </div>

                {/* Connector Line */}
                {index < steps.length - 1 && (
                  <div className={`
                    hidden sm:block w-16 h-0.5 ml-4 transition-all duration-200
                    ${step.number < currentStep ? 'bg-success-500' : 'bg-gray-200'}
                  `} />
                )}
              </div>
            ))}
          </div>

          {/* Mobile Step Indicator */}
          <div className="sm:hidden mt-4">
            <div className="flex justify-center">
              <div className="bg-gray-200 rounded-full h-2 w-full max-w-xs">
                <div 
                  className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(currentStep / steps.length) * 100}%` }}
                />
              </div>
            </div>
            <div className="text-center mt-2">
              <p className="font-brand font-medium text-sm text-primary-700">
                {steps[currentStep - 1]?.title}
              </p>
              <p className="text-xs text-gray-500">{steps[currentStep - 1]?.description}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 py-8 sm:px-6">
        <div className="bg-white rounded-xl shadow-soft p-6 sm:p-8">
          {children}
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-8">
        <div className="max-w-4xl mx-auto px-4 py-4 sm:px-6">
          <div className="flex items-center justify-center text-center">
            <p className="text-xs text-gray-500">
              🔒 Your data is secure and private • 
              <span className="ml-1 text-primary-600">Bank-grade encryption</span>
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}