'use client'

import { useState } from 'react'
import { OnboardingData } from '@/app/onboarding/page'

interface Step3Props {
  data: OnboardingData
  updateData: (updates: Partial<OnboardingData>) => void
  onNext: () => void
  onPrev: () => void
}

export default function Step3BudgetAllocation({ data, updateData, onNext, onPrev }: Step3Props) {
  const [allocations, setAllocations] = useState(data.allocations)
  const [isDragging, setIsDragging] = useState<string | null>(null)

  // Ensure allocations always add up to 100%
  const normalizeAllocations = (newAllocations: typeof allocations) => {
    const total = Object.values(newAllocations).reduce((sum, val) => sum + val, 0)
    if (total === 100) return newAllocations
    
    // If total is not 100, proportionally adjust other categories
    const keys = Object.keys(newAllocations) as (keyof typeof allocations)[]
    
    // Distribute the difference proportionally across all categories
    const adjusted = { ...newAllocations }
    const factor = 100 / total
    
    keys.forEach(key => {
      adjusted[key] = Math.round(adjusted[key] * factor)
    })
    
    // Handle rounding errors - ensure exactly 100%
    const newTotal = Object.values(adjusted).reduce((sum, val) => sum + val, 0)
    if (newTotal !== 100) {
      const diff = 100 - newTotal
      // Add/subtract the difference to the largest category
      const largest = keys.reduce((max, key) => adjusted[key] > adjusted[max] ? key : max)
      adjusted[largest] += diff
    }
    
    return adjusted
  }

  const updateAllocation = (category: keyof typeof allocations, value: number) => {
    // Enforce minimum and maximum bounds
    const minValue = category === 'smile' ? 2 : 5  // Smile min 2%, others min 5%
    const maxValue = category === 'needs' ? 70 : category === 'goals' ? 50 : 40  // Reasonable maximums
    
    const clampedValue = Math.max(minValue, Math.min(maxValue, value))
    
    const newAllocations = { ...allocations, [category]: clampedValue }
    const normalized = normalizeAllocations(newAllocations)
    
    setAllocations(normalized)
    updateData({ allocations: normalized })
  }

  const getRecommendedRanges = () => ({
    needs: { min: 40, max: 60, recommended: '45-55%' },
    wants: { min: 10, max: 30, recommended: '15-25%' },
    goals: { min: 15, max: 40, recommended: '20-30%' },
    smile: { min: 2, max: 15, recommended: '5-10%' }
  })

  const categories = [
    {
      key: 'needs' as const,
      name: 'NEEDS',
      icon: '🏠',
      description: 'Essential living expenses',
      examples: 'Housing, transport, groceries, healthcare',
      color: 'needs',
      bgColor: 'bg-needs-50',
      borderColor: 'border-needs-200',
      textColor: 'text-needs-700',
      sliderColor: 'bg-needs-500'
    },
    {
      key: 'wants' as const,
      name: 'WANTS',
      icon: '🎉',
      description: 'Nice-to-have spending',
      examples: 'Entertainment, dining out, shopping',
      color: 'wants',
      bgColor: 'bg-wants-50',
      borderColor: 'border-wants-200',
      textColor: 'text-wants-700',
      sliderColor: 'bg-wants-500'
    },
    {
      key: 'goals' as const,
      name: 'GOALS',
      icon: '🎯',
      description: 'Future-building investments',
      examples: 'Emergency fund, house deposit, investments',
      color: 'goals',
      bgColor: 'bg-goals-50',
      borderColor: 'border-goals-200',
      textColor: 'text-goals-700',
      sliderColor: 'bg-goals-500'
    },
    {
      key: 'smile' as const,
      name: 'SMILE',
      icon: '😊',
      description: 'Pure joy and happiness fund',
      examples: 'Travel, experiences, family treats',
      color: 'smile',
      bgColor: 'bg-smile-50',
      borderColor: 'border-smile-200',
      textColor: 'text-smile-700',
      sliderColor: 'bg-smile-500'
    }
  ]

  // const calculateAmounts = () => {
  //   return categories.map(category => ({
  //     ...category,
  //     percentage: allocations[category.key],
  //     amount: Math.round((data.totalMonthlyIncome * allocations[category.key]) / 100)
  //   }))
  // }

  const ranges = getRecommendedRanges()

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-goals-100 rounded-full mb-4">
          <span className="text-3xl">🎯</span>
        </div>
        <h1 className="font-brand font-bold text-3xl text-gray-800 mb-2">
          Set your budget allocation
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Adjust the balance between your needs, wants, goals, and happiness. 
          We&apos;ll start with recommended percentages, but you can customize them for your family.
        </p>
      </div>

      {/* Monthly Income Summary */}
      <div className="mb-8 p-4 bg-gradient-to-r from-primary-50 to-success-50 border border-primary-200 rounded-xl">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <span className="text-2xl">💰</span>
            <div>
              <p className="font-brand font-medium text-gray-700">Monthly Income to Allocate</p>
              <p className="text-sm text-gray-600">From your {data.incomeSourcess.length} income source{data.incomeSourcess.length !== 1 ? 's' : ''}</p>
            </div>
          </div>
          <span className="font-data font-bold text-2xl text-primary-700">
            ${data.totalMonthlyIncome.toLocaleString()}
          </span>
        </div>
      </div>

      {/* Budget Categories */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {categories.map((category) => {
          const amount = Math.round((data.totalMonthlyIncome * allocations[category.key]) / 100)
          const range = ranges[category.key]
          const isInRange = allocations[category.key] >= range.min && allocations[category.key] <= range.max
          
          return (
            <div key={category.key} className={`${category.bgColor} ${category.borderColor} border rounded-xl p-6`}>
              {/* Category Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <span className="text-2xl">{category.icon}</span>
                  <div>
                    <h3 className={`font-brand font-semibold text-lg ${category.textColor}`}>
                      {category.name}
                    </h3>
                    <p className="text-sm text-gray-600">{category.description}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-data font-bold text-2xl text-gray-800">
                    {allocations[category.key]}%
                  </div>
                  <div className="font-data font-medium text-lg text-gray-700">
                    ${amount.toLocaleString()}
                  </div>
                </div>
              </div>

              {/* Slider */}
              <div className="mb-4">
                <div className="relative">
                  <input
                    type="range"
                    min={range.min}
                    max={range.max}
                    value={allocations[category.key]}
                    onChange={(e) => updateAllocation(category.key, parseInt(e.target.value))}
                    onMouseDown={() => setIsDragging(category.key)}
                    onMouseUp={() => setIsDragging(null)}
                    onTouchStart={() => setIsDragging(category.key)}
                    onTouchEnd={() => setIsDragging(null)}
                    className={`
                      w-full h-3 rounded-lg appearance-none cursor-pointer
                      bg-gray-200 slider
                      ${isDragging === category.key ? 'ring-2 ring-primary-500' : ''}
                    `}
                    style={{
                      background: `linear-gradient(to right, rgb(var(--${category.color}-500)) 0%, rgb(var(--${category.color}-500)) ${((allocations[category.key] - range.min) / (range.max - range.min)) * 100}%, #e5e7eb ${((allocations[category.key] - range.min) / (range.max - range.min)) * 100}%, #e5e7eb 100%)`
                    }}
                  />
                  <style jsx>{`
                    .slider::-webkit-slider-thumb {
                      appearance: none;
                      width: 24px;
                      height: 24px;
                      border-radius: 50%;
                      background: white;
                      border: 3px solid rgb(var(--${category.color}-500));
                      cursor: pointer;
                      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                      transition: all 0.2s ease;
                    }
                    .slider::-webkit-slider-thumb:hover {
                      transform: scale(1.1);
                      box-shadow: 0 4px 8px rgba(0,0,0,0.15);
                    }
                    .slider::-moz-range-thumb {
                      width: 24px;
                      height: 24px;
                      border-radius: 50%;
                      background: white;
                      border: 3px solid rgb(var(--${category.color}-500));
                      cursor: pointer;
                      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    }
                  `}</style>
                </div>

                {/* Range Indicators */}
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>{range.min}%</span>
                  <span className={`font-medium ${isInRange ? 'text-success-600' : 'text-amber-600'}`}>
                    Recommended: {range.recommended}
                  </span>
                  <span>{range.max}%</span>
                </div>
              </div>

              {/* Examples */}
              <p className="text-sm text-gray-600">
                <span className="font-medium">Examples:</span> {category.examples}
              </p>

              {/* Validation Message */}
              {!isInRange && (
                <div className="mt-3 p-2 bg-amber-50 border border-amber-200 rounded-lg">
                  <p className="text-xs text-amber-700">
                    💡 Consider adjusting to the recommended range for a balanced budget
                  </p>
                </div>
              )}
            </div>
          )
        })}
      </div>

      {/* Total Validation */}
      <div className="mb-8 p-4 bg-white border-2 border-gray-200 rounded-xl">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-brand font-semibold text-lg text-gray-800">Total Allocation</h4>
            <p className="text-sm text-gray-600">Must equal 100% to continue</p>
          </div>
          <div className="text-right">
            <div className={`font-data font-bold text-3xl ${
              Object.values(allocations).reduce((sum, val) => sum + val, 0) === 100 
                ? 'text-success-600' 
                : 'text-red-600'
            }`}>
              {Object.values(allocations).reduce((sum, val) => sum + val, 0)}%
            </div>
            {Object.values(allocations).reduce((sum, val) => sum + val, 0) === 100 && (
              <p className="text-sm text-success-600 font-medium">✓ Perfect balance!</p>
            )}
          </div>
        </div>
      </div>

      {/* Smile Philosophy Reminder */}
      <div className="mb-8 p-6 bg-gradient-to-r from-smile-50 to-primary-50 border border-smile-200 rounded-xl">
        <div className="flex items-start gap-4">
          <span className="text-3xl">😊</span>
          <div>
            <h4 className="font-brand font-semibold text-lg text-smile-800 mb-2">
              Remember the Smile Philosophy
            </h4>
            <p className="text-smile-700 mb-3">
              Your Smile fund isn&apos;t &quot;leftover money&quot; - it&apos;s an intentional investment in your family&apos;s happiness and well-being.
            </p>
            <div className="text-sm text-smile-600">
              <p>• <strong>Travel:</strong> Create lasting family memories</p>
              <p>• <strong>Experiences:</strong> Try new things together</p>
              <p>• <strong>Treats:</strong> Celebrate life&apos;s small moments</p>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex justify-between items-center pt-6 border-t border-gray-200">
        <button
          onClick={onPrev}
          className="px-6 py-2 border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50 transition-colors"
        >
          ← Back
        </button>
        
        <button
          onClick={onNext}
          disabled={Object.values(allocations).reduce((sum, val) => sum + val, 0) !== 100}
          className={`
            btn-primary px-8 py-3 rounded-lg font-brand font-semibold transition-all duration-200
            ${Object.values(allocations).reduce((sum, val) => sum + val, 0) !== 100
              ? 'opacity-50 cursor-not-allowed' 
              : 'hover:transform hover:-translate-y-0.5'
            }
          `}
        >
          Continue to Categories →
        </button>
      </div>
    </div>
  )
}