'use client'

import { useState } from 'react'
import { OnboardingData } from '@/app/onboarding/page'

interface Step1Props {
  data: OnboardingData
  updateData: (updates: Partial<OnboardingData>) => void
  onNext: () => void
}

export default function Step1AboutYou({ data, updateData, onNext }: Step1Props) {
  const [errors, setErrors] = useState<{[key: string]: string}>({})

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {}
    
    if (!data.householdName.trim()) {
      newErrors.householdName = 'Household name is required'
    }
    
    if (!data.yourName.trim()) {
      newErrors.yourName = 'Your name is required'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validateForm()) {
      onNext()
    }
  }

  const updateHouseholdComposition = (field: keyof OnboardingData['householdComposition'], value: number | boolean) => {
    updateData({
      householdComposition: {
        ...data.householdComposition,
        [field]: value
      }
    })
  }

  return (
    <div className="max-w-2xl mx-auto">
      {/* Welcome Header */}
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-100 rounded-full mb-4">
          <span className="text-3xl">👋</span>
        </div>
        <h1 className="font-brand font-bold text-3xl text-gray-800 mb-2">
          Welcome to Smile Budget!
        </h1>
        <p className="text-lg text-gray-600 max-w-lg mx-auto">
          Let&apos;s start by learning about your household. This helps us create a budget that fits your family perfectly.
        </p>
      </div>

      <div className="space-y-8">
        {/* Household Name */}
        <div>
          <label className="block font-brand font-semibold text-sm text-gray-700 mb-2">
            What would you like to call your household?
          </label>
          <input
            type="text"
            value={data.householdName}
            onChange={(e) => updateData({ householdName: e.target.value })}
            placeholder="e.g., The Smith Family, Our Home, Johnson Household"
            className={`
              mobile-input-text w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors
              ${errors.householdName ? 'border-red-300 bg-red-50' : 'border-gray-300'}
            `}
          />
          {errors.householdName && (
            <p className="text-red-600 text-sm mt-1">{errors.householdName}</p>
          )}
          <p className="text-gray-500 text-sm mt-1">
            This is how we&apos;ll refer to your family in the app
          </p>
        </div>

        {/* Your Name */}
        <div>
          <label className="block font-brand font-semibold text-sm text-gray-700 mb-2">
            What&apos;s your name?
          </label>
          <input
            type="text"
            value={data.yourName}
            onChange={(e) => updateData({ yourName: e.target.value })}
            placeholder="Your first name or nickname"
            className={`
              mobile-input-text w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors
              ${errors.yourName ? 'border-red-300 bg-red-50' : 'border-gray-300'}
            `}
          />
          {errors.yourName && (
            <p className="text-red-600 text-sm mt-1">{errors.yourName}</p>
          )}
        </div>

        {/* Household Composition */}
        <div>
          <h3 className="font-brand font-semibold text-lg text-gray-800 mb-4">
            Tell us about your household composition
          </h3>
          
          <div className="space-y-4">
            {/* Adults */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <span className="text-2xl">👨‍👩‍</span>
                <div>
                  <p className="font-brand font-medium text-gray-800">Adults</p>
                  <p className="text-sm text-gray-600">Including yourself</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <button
                  onClick={() => updateHouseholdComposition('adults', Math.max(1, data.householdComposition.adults - 1))}
                  className="w-8 h-8 rounded-full bg-white border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-100 transition-colors"
                  disabled={data.householdComposition.adults <= 1}
                >
                  −
                </button>
                <span className="font-data font-medium text-lg w-8 text-center">
                  {data.householdComposition.adults}
                </span>
                <button
                  onClick={() => updateHouseholdComposition('adults', Math.min(10, data.householdComposition.adults + 1))}
                  className="w-8 h-8 rounded-full bg-white border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-100 transition-colors"
                >
                  +
                </button>
              </div>
            </div>

            {/* Children */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <span className="text-2xl">👶</span>
                <div>
                  <p className="font-brand font-medium text-gray-800">Children</p>
                  <p className="text-sm text-gray-600">Kids living at home</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <button
                  onClick={() => updateHouseholdComposition('children', Math.max(0, data.householdComposition.children - 1))}
                  className="w-8 h-8 rounded-full bg-white border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-100 transition-colors"
                  disabled={data.householdComposition.children <= 0}
                >
                  −
                </button>
                <span className="font-data font-medium text-lg w-8 text-center">
                  {data.householdComposition.children}
                </span>
                <button
                  onClick={() => updateHouseholdComposition('children', Math.min(10, data.householdComposition.children + 1))}
                  className="w-8 h-8 rounded-full bg-white border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-100 transition-colors"
                >
                  +
                </button>
              </div>
            </div>

            {/* Pets */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <span className="text-2xl">🐕</span>
                <div>
                  <p className="font-brand font-medium text-gray-800">Pets</p>
                  <p className="text-sm text-gray-600">Furry family members</p>
                </div>
              </div>
              <button
                onClick={() => updateHouseholdComposition('pets', !data.householdComposition.pets)}
                className={`
                  relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2
                  ${data.householdComposition.pets ? 'bg-primary-600' : 'bg-gray-200'}
                `}
              >
                <span
                  className={`
                    pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out
                    ${data.householdComposition.pets ? 'translate-x-5' : 'translate-x-0'}
                  `}
                />
              </button>
            </div>
          </div>
        </div>

        {/* Smile Philosophy Introduction */}
        <div className="bg-gradient-to-r from-primary-50 to-smile-50 border border-primary-200 rounded-xl p-6">
          <div className="flex items-start gap-4">
            <span className="text-3xl">😊</span>
            <div>
              <h4 className="font-brand font-semibold text-lg text-primary-800 mb-2">
                The Smile Philosophy
              </h4>
              <p className="text-primary-700 mb-3">
                We believe budgeting should bring joy, not stress. That&apos;s why we help you allocate money for happiness alongside your needs and goals.
              </p>
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div className="flex items-center gap-2">
                  <span className="text-needs-600">🏠</span>
                  <span className="text-needs-700">Needs (essentials)</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-wants-600">🎉</span>
                  <span className="text-wants-700">Wants (lifestyle)</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-goals-600">🎯</span>
                  <span className="text-goals-700">Goals (future)</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-smile-600">😊</span>
                  <span className="text-smile-700">Smile (pure joy)</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Next Button */}
      <div className="flex justify-end mt-8 pt-6 border-t border-gray-200">
        <button
          onClick={handleNext}
          disabled={!data.householdName.trim() || !data.yourName.trim()}
          className={`
            btn-primary px-8 py-3 rounded-lg font-brand font-semibold transition-all duration-200
            ${(!data.householdName.trim() || !data.yourName.trim()) 
              ? 'opacity-50 cursor-not-allowed' 
              : 'hover:transform hover:-translate-y-0.5'
            }
          `}
        >
          Continue to Income →
        </button>
      </div>
    </div>
  )
}