'use client'

import { useState } from 'react'
import { OnboardingData } from '@/app/onboarding/page'

interface Step2Props {
  data: OnboardingData
  updateData: (updates: Partial<OnboardingData>) => void
  onNext: () => void
  onPrev: () => void
}

type IncomeSource = OnboardingData['incomeSourcess'][0]

export default function Step2IncomeInput({ data, updateData, onNext, onPrev }: Step2Props) {
  const [isAdding, setIsAdding] = useState(false)
  const [newIncome, setNewIncome] = useState<Partial<IncomeSource>>({
    name: '',
    type: 'salary',
    frequency: 'monthly',
    amount: 0
  })

  const incomeTypes = [
    { value: 'salary', label: 'Salary/Wage', icon: '💼' },
    { value: 'freelance', label: 'Freelance', icon: '💻' },
    { value: 'investment', label: 'Investment', icon: '📈' },
    { value: 'rental', label: 'Rental Property', icon: '🏠' },
    { value: 'business', label: 'Business', icon: '🏢' },
    { value: 'pension', label: 'Pension', icon: '👴' },
    { value: 'other', label: 'Other', icon: '💰' }
  ]

  const frequencies = [
    { value: 'weekly', label: 'Weekly', multiplier: 52 },
    { value: 'fortnightly', label: 'Fortnightly', multiplier: 26 },
    { value: 'monthly', label: 'Monthly', multiplier: 12 },
    { value: 'quarterly', label: 'Quarterly', multiplier: 4 },
    { value: 'annually', label: 'Annually', multiplier: 1 }
  ]

  const calculateMonthlyTotal = () => {
    return data.incomeSourcess.reduce((total, income) => {
      const freq = frequencies.find(f => f.value === income.frequency)
      const monthlyAmount = freq ? (income.amount * freq.multiplier) / 12 : 0
      return total + monthlyAmount
    }, 0)
  }

  const addIncomeSource = () => {
    if (newIncome.name && newIncome.amount && newIncome.amount > 0) {
      const incomeSource: IncomeSource = {
        id: Date.now().toString(),
        name: newIncome.name,
        type: newIncome.type as IncomeSource['type'],
        frequency: newIncome.frequency as IncomeSource['frequency'],
        amount: newIncome.amount
      }
      
      const updatedSources = [...data.incomeSourcess, incomeSource]
      updateData({ 
        incomeSourcess: updatedSources,
        totalMonthlyIncome: calculateMonthlyTotal()
      })
      
      setNewIncome({ name: '', type: 'salary', frequency: 'monthly', amount: 0 })
      setIsAdding(false)
    }
  }

  const removeIncomeSource = (id: string) => {
    const updatedSources = data.incomeSourcess.filter(income => income.id !== id)
    updateData({ 
      incomeSourcess: updatedSources,
      totalMonthlyIncome: calculateMonthlyTotal()
    })
  }

  const getMonthlyAmount = (income: IncomeSource) => {
    const freq = frequencies.find(f => f.value === income.frequency)
    return freq ? (income.amount * freq.multiplier) / 12 : 0
  }

  const handleNext = () => {
    const totalMonthly = calculateMonthlyTotal()
    updateData({ totalMonthlyIncome: totalMonthly })
    onNext()
  }

  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-success-100 rounded-full mb-4">
          <span className="text-3xl">💰</span>
        </div>
        <h1 className="font-brand font-bold text-3xl text-gray-800 mb-2">
          Let&apos;s add your income sources
        </h1>
        <p className="text-lg text-gray-600">
          Add all sources of income for your household. We&apos;ll calculate your monthly total.
        </p>
      </div>

      {/* Current Income Sources */}
      {data.incomeSourcess.length > 0 && (
        <div className="mb-8">
          <h3 className="font-brand font-semibold text-lg text-gray-800 mb-4">
            Your Income Sources
          </h3>
          <div className="space-y-3">
            {data.incomeSourcess.map((income) => {
              const typeInfo = incomeTypes.find(t => t.value === income.type)
              const freqInfo = frequencies.find(f => f.value === income.frequency)
              
              return (
                <div key={income.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border">
                  <div className="flex items-center gap-3">
                    <span className="text-2xl">{typeInfo?.icon}</span>
                    <div>
                      <p className="font-brand font-medium text-gray-800">{income.name}</p>
                      <p className="text-sm text-gray-600">
                        ${income.amount.toLocaleString()} {freqInfo?.label.toLowerCase()} • 
                        ${getMonthlyAmount(income).toLocaleString()}/month
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => removeIncomeSource(income.id)}
                    className="text-red-500 hover:text-red-700 p-2"
                  >
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              )
            })}
          </div>

          {/* Total Monthly Income */}
          <div className="mt-6 p-4 bg-gradient-to-r from-success-50 to-primary-50 border border-success-200 rounded-xl">
            <div className="flex justify-between items-center">
              <span className="font-brand font-semibold text-lg text-gray-800">
                Total Monthly Income
              </span>
              <span className="font-data font-bold text-2xl text-primary-700">
                ${calculateMonthlyTotal().toLocaleString()}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Add Income Form */}
      {isAdding ? (
        <div className="mb-8 p-6 border-2 border-dashed border-primary-300 rounded-xl">
          <h3 className="font-brand font-semibold text-lg text-gray-800 mb-4">
            Add Income Source
          </h3>
          
          <div className="space-y-4">
            {/* Income Name */}
            <div>
              <label className="block font-brand font-medium text-sm text-gray-700 mb-1">
                Income Source Name
              </label>
              <input
                type="text"
                value={newIncome.name || ''}
                onChange={(e) => setNewIncome({ ...newIncome, name: e.target.value })}
                placeholder="e.g., My Salary, Partner&apos;s Job, Rental Income"
                className="mobile-input-text w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            {/* Income Type */}
            <div>
              <label className="block font-brand font-medium text-sm text-gray-700 mb-2">
                Type
              </label>
              <div className="grid grid-cols-3 gap-2">
                {incomeTypes.map((type) => (
                  <button
                    key={type.value}
                    onClick={() => setNewIncome({ ...newIncome, type: type.value as IncomeSource['type'] })}
                    className={`
                      p-3 rounded-lg border text-center transition-all duration-200
                      ${newIncome.type === type.value
                        ? 'border-primary-500 bg-primary-50 text-primary-700'
                        : 'border-gray-300 hover:border-gray-400'
                      }
                    `}
                  >
                    <div className="text-lg mb-1">{type.icon}</div>
                    <div className="text-xs font-medium">{type.label}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* Amount and Frequency */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block font-brand font-medium text-sm text-gray-700 mb-1">
                  Amount ($)
                </label>
                <input
                  type="number"
                  value={newIncome.amount || ''}
                  onChange={(e) => setNewIncome({ ...newIncome, amount: parseFloat(e.target.value) || 0 })}
                  placeholder="0"
                  className="mobile-input-text w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              
              <div>
                <label className="block font-brand font-medium text-sm text-gray-700 mb-1">
                  Frequency
                </label>
                <select
                  value={newIncome.frequency || 'monthly'}
                  onChange={(e) => setNewIncome({ ...newIncome, frequency: e.target.value as IncomeSource['frequency'] })}
                  className="mobile-input-text w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  {frequencies.map((freq) => (
                    <option key={freq.value} value={freq.value}>{freq.label}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Monthly Equivalent */}
            {newIncome.amount && newIncome.amount > 0 && (
              <div className="p-3 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600">
                  Monthly equivalent: <span className="font-medium text-gray-800">
                    ${((newIncome.amount * (frequencies.find(f => f.value === newIncome.frequency)?.multiplier || 12)) / 12).toLocaleString()}
                  </span>
                </p>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-3 pt-2">
              <button
                onClick={addIncomeSource}
                disabled={!newIncome.name || !newIncome.amount || newIncome.amount <= 0}
                className="btn-primary px-4 py-2 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Add Income
              </button>
              <button
                onClick={() => {
                  setIsAdding(false)
                  setNewIncome({ name: '', type: 'salary', frequency: 'monthly', amount: 0 })
                }}
                className="px-4 py-2 border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      ) : (
        <div className="mb-8">
          <button
            onClick={() => setIsAdding(true)}
            className="w-full p-6 border-2 border-dashed border-gray-300 rounded-xl hover:border-primary-400 hover:bg-primary-50 transition-colors group"
          >
            <div className="text-center">
              <div className="text-3xl mb-2 group-hover:scale-110 transition-transform">➕</div>
              <p className="font-brand font-medium text-gray-700 group-hover:text-primary-700">
                Add Income Source
              </p>
            </div>
          </button>
        </div>
      )}

      {/* Navigation */}
      <div className="flex justify-between items-center pt-6 border-t border-gray-200">
        <button
          onClick={onPrev}
          className="px-6 py-2 border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50 transition-colors"
        >
          ← Back
        </button>
        
        <button
          onClick={handleNext}
          disabled={data.incomeSourcess.length === 0}
          className={`
            btn-primary px-8 py-3 rounded-lg font-brand font-semibold transition-all duration-200
            ${data.incomeSourcess.length === 0 
              ? 'opacity-50 cursor-not-allowed' 
              : 'hover:transform hover:-translate-y-0.5'
            }
          `}
        >
          Continue to Budget Allocation →
        </button>
      </div>
    </div>
  )
}