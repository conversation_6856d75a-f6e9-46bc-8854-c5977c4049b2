-- Fix RLS Policies - Remove Circular References
-- Run this in your Supabase SQL Editor to fix the infinite recursion

-- First, drop all existing policies that might be causing circular references
DROP POLICY IF EXISTS "Users can view their own household" ON public.households;
DROP POLICY IF EXISTS "Users can insert households" ON public.households;
DROP POLICY IF EXISTS "Users can update their own household" ON public.households;

DROP POLICY IF EXISTS "Users can view profiles in their household" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.user_profiles;

DROP POLICY IF EXISTS "Users can view household income sources" ON public.income_sources;
DROP POLICY IF EXISTS "Users can insert household income sources" ON public.income_sources;
DROP POLICY IF EXISTS "Users can update household income sources" ON public.income_sources;

DROP POLICY IF EXISTS "Users can view household budget periods" ON public.budget_periods;
DROP POLICY IF EXISTS "Users can insert household budget periods" ON public.budget_periods;
DROP POLICY IF EXISTS "Users can update household budget periods" ON public.budget_periods;

DROP POLICY IF EXISTS "Users can view household budget categories" ON public.budget_categories;
DROP POLICY IF EXISTS "Users can insert household budget categories" ON public.budget_categories;
DROP POLICY IF EXISTS "Users can update household budget categories" ON public.budget_categories;

DROP POLICY IF EXISTS "Users can view household expenses" ON public.expenses;
DROP POLICY IF EXISTS "Users can insert their own expenses" ON public.expenses;
DROP POLICY IF EXISTS "Users can update their own expenses" ON public.expenses;

-- Temporarily disable RLS to allow initial setup
ALTER TABLE public.households DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.income_sources DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.budget_periods DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.budget_categories DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.expenses DISABLE ROW LEVEL SECURITY;