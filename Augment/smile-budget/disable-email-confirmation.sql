-- Disable email confirmation for smooth user experience
-- Run this in your Supabase SQL Editor

-- This updates the auth configuration to disable email confirmation
-- Note: This may not be available in all Supabase instances, but worth trying

-- Alternative: You should disable email confirmation in the Supabase Dashboard
-- Go to: Authentication > Settings > Uncheck "Enable email confirmations"

-- If you need to confirm existing users manually, you can run:
-- UPDATE auth.users SET email_confirmed_at = NOW() WHERE email_confirmed_at IS NULL;

-- Check current auth settings
SELECT * FROM auth.config;